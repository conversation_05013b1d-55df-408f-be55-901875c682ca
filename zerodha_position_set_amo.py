import utils
from broker import <PERSON><PERSON>
from croniter import croniter
from datetime import datetime
import time
from filtered_instruments import FilteredInstruments
import traceback
import signal
import sys
signal.signal(signal.SIGINT, lambda x, y: sys.exit(0))
import signal
import sys
signal.signal(signal.SIGINT, lambda x, y: sys.exit(0))

class ZerodhaPositionSqoffAmo:
    def __init__(self):
        self.config = utils.get_config()
        self.broker = Zerodha(
            self.config.zerodha_username,
            api_key=self.config.zerodha_api_key,
            access_token=self.config.zerodha_access_token
        )
        self.filtered_instruments = FilteredInstruments(self.broker).df_raw


    def task(self):
        positions = self.broker.positions()
        open_positions = [p for p in positions if p['quantity']!=0]
        symbols_with_open_orders = [o['tradingsymbol'] for o in self.broker.orders() if o['status'] in ('OPEN', 'AMO REQ RECEIVED', 'OPEN PENDING', 'TRIGGER PENDING')]
        for p in open_positions:
            if p['tradingsymbol'] in symbols_with_open_orders:
                continue
            else:
                print("placing_sell_order_for: ", p['tradingsymbol'])
                script_tick_size = self.filtered_instruments.loc[self.filtered_instruments['tradingsymbol'] == p['tradingsymbol'], 'tick_size'].values[0]
                price = int(p['buy_price']*1.5/script_tick_size)*script_tick_size
                self.broker.place_order(tradingsymbol=p['tradingsymbol'], transaction_type='SELL', quantity=p['quantity'],
            product='NRML', order_type='LIMIT', price=price, variety='amo')
            time.sleep(2)

# schedule = "* * * * *"
# cron = croniter(schedule, datetime.now())

z = ZerodhaPositionSqoffAmo()

# while True:
    # next_execution = cron.get_next(datetime)
    # print(next_execution)
    # time.sleep((next_execution - datetime.now()).total_seconds()+10)
z.task()
