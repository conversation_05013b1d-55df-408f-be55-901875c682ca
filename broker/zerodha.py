# from kiteext import KiteExt
from kiteconnect import KiteConnect, KiteTicker
# from kiteconnect_extras import KiteExt
# from kiteconnect_extras import KiteExtTicker
from .interfaces import BrokerI
import pyotp
import requests
import yaml
import os
from datetime import datetime
from urllib.parse import urlparse, parse_qs

class Zerodha(BrokerI):
    def __init__(self, username, password=None, totp_secret=None, api_key = None, access_token = None, api_secret=None):
        self.kite = KiteConnect(api_key=api_key)
        if access_token:
            self.kite.set_access_token(access_token=access_token)
        elif username and password and totp_secret and api_secret:
            session = requests.Session()
            session_id = parse_qs(urlparse(session.get(self.kite.login_url()).url).query)['sess_id'][0]
            request_id = session.post("https://kite.zerodha.com/api/login", {"user_id": username, "password": password}).json()["data"]["request_id"]
            session.post("https://kite.zerodha.com/api/twofa", {"user_id": username, "request_id": request_id, "twofa_value": pyotp.TOTP(totp_secret).now(), "skip_session": True})
            r = session.get("https://kite.zerodha.com/connect/finish?api_key=k73ue5uj00cpn3vc&sess_id="+session_id+"&skip_session=true", allow_redirects=False)
            request_token = parse_qs(urlparse(r.headers['location']).query)['request_token'][0]
            access_token = self.kite.generate_session(request_token=request_token, api_secret=api_secret)['access_token']            
            self.kite.set_access_token(access_token=access_token)
            with open('config.yml', 'r') as file:
                config = yaml.safe_load(file)
            config['zerodha_access_token'] = self.kite.access_token
            with open('config.yml', 'w') as file:
                yaml.safe_dump(config, file)
        else:
            raise NotImplementedError


    def name(self):
        return 'zerodha'

    def get_ltp(self, instruments, exchange=None):
        if not isinstance(instruments, list):
            instruments = [instruments]
        if not exchange:
            exchange = 'NFO' 
        instruments = [exchange+":"+i for i in instruments]
        quotes = self.kite.ltp(instruments)
        return quotes

    def get_quote(self, tradingsymbol):
        return self.kite.quote(tradingsymbol)

    def get_bulk_quote(self, instruments, exchange='NFO'):
        instruments = [exchange+":"+i for i in instruments]
        quotes = self.kite.quote(instruments)
        subscript = len(exchange)+1
        quotes = {k[subscript:]: v for k, v in quotes.items()}

        instruments = [i[subscript:] for i in instruments]
        quotes = {i:quotes[i]['last_price'] if i in quotes else None for i in instruments}
        return quotes

    def positions(self):
        return self.kite.positions()['net']

    def ticker(self):
        # return self.kite.ticker()

        #for kiteconnect_extras
        return KiteTicker(
            user_id=self.kite.user_id,
            enctoken=self.kite.enctoken
        )

    def orders(self):
        return self.kite.orders()

    def instruments(self, exchange=None):
        return self.kite.instruments(exchange=exchange)

    def place_order(self, tradingsymbol, transaction_type, quantity, product, order_type, price=None,
                    trigger_price=None, variety=None, exchange=None):
        if not exchange:
            exchange = 'NFO'
        if not variety:
            variety = 'regular'
        print(datetime.now(),  "received_order: ", "variety: ", variety, 
              "tradingsymbol:", tradingsymbol, 
              "transaction_type: ", transaction_type, 
              "quantity: ", quantity,
              "product: ", product, 
              "order_type: ", order_type,
              "price: ", price, 
              "trigger_price: ", trigger_price
            )
        return self.kite.place_order(variety=variety,
                                     exchange=exchange,
                                     tradingsymbol=tradingsymbol,
                                     transaction_type=transaction_type,
                                     quantity=quantity,
                                     product=product,
                                     order_type=order_type,
                                     price=price,
                                     trigger_price=trigger_price
                                     )

    def modify_order(self, order_id, price, trigger_price=None):
        return self.kite.modify_order(variety='regular', order_id=order_id, price=price, trigger_price=trigger_price)

    def cancel_order(self, order_id):
        return self.kite.cancel_order(variety='regular', order_id=order_id, parent_order_id='')

    def place_sl_target_gtt(self, tradingsymbol, quantity, curr_price, sl_price, target_price, product='NRML', transaction_type='SELL'):
        return self.kite.place_gtt(trigger_type='two-leg',
                                   tradingsymbol=tradingsymbol,
                                   exchange='NFO',
                                   trigger_values=[sl_price, target_price],
                                   last_price=curr_price,
                                   orders=[
                                       {
                                           'transaction_type': transaction_type,
                                           'quantity': quantity,
                                           'order_type': 'LIMIT',
                                           'product': product,
                                           'price': sl_price-1
                                       },
                                       {
                                           'transaction_type': transaction_type,
                                           'quantity': quantity,
                                           'order_type': 'LIMIT',
                                           'product': product,
                                           'price': target_price-1
                                       },
                                   ]
                                   )

    def get_gtts(self):
        return self.kite.get_gtts()

    def get_margin(self):
        return self.kite.margins(segment='equity')
