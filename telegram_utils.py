from telethon import TelegramClient, events, sync
import telegram
from datetime import datetime, timedelta
from pytz import timezone
ist = timezone('Asia/Kolkata')

class TelegramUtil:
    def __init__(self, config, client=True, bot=True, session_name='session_name') -> None:
        if bot:
            self.bot = telegram.Bot(token=config.telegram_bot_token)
        else:
            self.bot = None
        if client:
            self.client = TelegramClient(session_name,
                                        config.telegram_api_id,
                                        config.telegram_api_hash)
            self.client.start(phone=config.telegram_phone_number)
        else:
            self.client = None
        # self.client.start(bot_token=config.telegram_bot_token)

    async def send_message(self, chatid, msg, reply_to=None):
        if self.client:
            await self.client.send_message(chatid, msg, reply_to=reply_to, parse_mode='html')
        elif self.bot:
            await self.bot.send_message(chat_id=chatid, text=msg, parse_mode='html')

    def read_todays_messages(self, chatid):
        if not self.client:
            return
        today = datetime.now(ist).replace(hour=9, minute=0, second=0, microsecond=0)
        # today = today - timedelta(days=2)
        return self.client.get_messages(chatid, offset_date=today, limit=1000, reverse=True)
