import utils
from broker import Zerodha
import pika

class RabbitMQChannelSingleton:
    # _self = None

    # def init_channel(self):
    #     config = utils.get_config()
    #     self.mq_connection = pika.BlockingConnection(pika.ConnectionParameters('localhost', heartbeat=10))
    #     self.mq_channel = self.mq_connection.channel()
    #     self.mq_channel.queue_declare(queue='quotes')
    #     print("rabbitmq_channel_created")

    # def __new__(cls):
    #     if cls._self is None:
    #         cls._self = super().__new__(cls)
    #         cls._self.init_channel()
    #     return cls._self

    # def __getattr__(self, attr):
    #     return getattr(self.mq_channel, attr)
    def __init__(self):
        config = utils.get_config()
        self.mq_connection = pika.BlockingConnection(pika.ConnectionParameters('localhost', heartbeat=10))
        self.mq_channel = self.mq_connection.channel()
        self.mq_channel.queue_declare(queue='quotes')
        print("rabbitmq_channel_created")
