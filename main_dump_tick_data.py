import random
import datetime
import time
import psycopg2

# Connect to the database
conn = psycopg2.connect(host="127.0.0.1", database="algo", user="postgres", port=5434)
cursor = conn.cursor()

# Generate some random data
symbols = ["ABC", "DEF", "GHI", "JKL", "MNO"]
start_time = datetime.datetime.now()
end_time = start_time + datetime.timedelta(seconds=3600)

while datetime.datetime.now() < end_time:
    timestamp = datetime.datetime.now()
    symbol = random.choice(symbols)
    price = random.uniform(10, 100)
    quantity = random.randint(1, 10)
    data = (timestamp, symbol, price, quantity)

    # Insert the data into the table
    cursor.execute('INSERT INTO algo_tick_data (time, symbol, price, quantity) VALUES (%s, %s, %s, %s)', data)

    # Save the changes
    conn.commit()

    # Wait for 1 second
    time.sleep(1)
