#!/usr/bin/env python3
"""
Test script for cron_ohlc_timeframes.py
Tests the timeframe population logic without running the full cron.
"""

import psycopg2
import utils
import logging
from datetime import datetime, time as dt_time
from cron_ohlc_timeframes import OHLCTimeframePopulator, is_outside_business_hours

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s %(levelname)s: %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S"
)

def test_business_hours():
    """Test business hours detection."""
    logging.info("--- Testing Business Hours Detection ---")
    
    # Test during business hours (assuming it's a weekday)
    now = datetime.now()
    is_outside = is_outside_business_hours()
    logging.info(f"Current time: {now}")
    logging.info(f"Is outside business hours: {is_outside}")
    
    return True

def test_timeframe_logic():
    """Test timeframe population logic."""
    logging.info("--- Testing Timeframe Logic ---")
    
    try:
        populator = OHLCTimeframePopulator()
        
        # Test different times
        test_times = [
            datetime(2024, 1, 15, 9, 20),   # 9:20 - should populate 5min
            datetime(2024, 1, 15, 9, 30),   # 9:30 - should populate 5min, 15min
            datetime(2024, 1, 15, 10, 30),  # 10:30 - should populate 5min, 1hour
            datetime(2024, 1, 15, 15, 30),  # 15:30 - should populate 5min, daily
        ]
        
        for test_time in test_times:
            logging.info(f"\nTesting time: {test_time}")
            
            timeframes_to_populate = []
            for timeframe in ['5min', '15min', '1hour', 'daily']:
                if populator.should_populate_timeframe(test_time, timeframe):
                    timeframes_to_populate.append(timeframe)
            
            logging.info(f"Timeframes to populate: {timeframes_to_populate}")
            
            # Test time bucket calculation
            for tf in timeframes_to_populate:
                bucket = populator.get_time_bucket(test_time, tf)
                logging.info(f"  {tf} bucket: {bucket}")
        
        populator.db_conn.close()
        return True
        
    except Exception as e:
        logging.error(f"Error in timeframe logic test: {e}")
        return False

def test_database_connection():
    """Test database connection and basic queries."""
    logging.info("--- Testing Database Connection ---")
    
    try:
        populator = OHLCTimeframePopulator()
        
        # Test basic connection
        with populator.db_conn.cursor() as cur:
            cur.execute("SELECT COUNT(*) FROM ohlc_1min")
            count_1min = cur.fetchone()[0]
            logging.info(f"Records in ohlc_1min: {count_1min}")
            
            # Check if we have recent data
            cur.execute("SELECT MAX(time) FROM ohlc_1min")
            latest_time = cur.fetchone()[0]
            logging.info(f"Latest time in ohlc_1min: {latest_time}")
            
            # Check sample symbols
            cur.execute("SELECT DISTINCT symbol FROM ohlc_1min LIMIT 5")
            sample_symbols = [row[0] for row in cur.fetchall()]
            logging.info(f"Sample symbols: {sample_symbols}")
        
        logging.info(f"NSE F&O stocks count: {len(populator.nse_stocks)}")
        logging.info(f"Sample NSE stocks: {populator.nse_stocks[:5]}")
        
        populator.db_conn.close()
        return True
        
    except Exception as e:
        logging.error(f"Database connection test failed: {e}")
        return False

def test_single_timeframe_population():
    """Test populating a single timeframe with sample data."""
    logging.info("--- Testing Single Timeframe Population ---")
    
    try:
        populator = OHLCTimeframePopulator()
        
        # Use current time rounded to 5-minute boundary for testing
        current_time = datetime.now()
        test_time = current_time.replace(
            minute=(current_time.minute // 5) * 5, 
            second=0, 
            microsecond=0
        )
        
        logging.info(f"Testing with time: {test_time}")
        
        # Test 5min population (most frequent)
        if populator.should_populate_timeframe(test_time, '5min'):
            logging.info("Populating 5min timeframe...")
            populator.populate_timeframe_for_symbols('5min', test_time)
            
            # Check results
            with populator.db_conn.cursor() as cur:
                cur.execute("SELECT COUNT(*) FROM ohlc_5min WHERE time = %s", (test_time,))
                count = cur.fetchone()[0]
                logging.info(f"Records inserted in ohlc_5min: {count}")
                
                if count > 0:
                    cur.execute("""
                        SELECT symbol, price, volume, oi 
                        FROM ohlc_5min 
                        WHERE time = %s 
                        LIMIT 3
                    """, (test_time,))
                    sample_data = cur.fetchall()
                    logging.info("Sample data:")
                    for row in sample_data:
                        logging.info(f"  {row}")
        else:
            logging.info("Current time doesn't require 5min population")
        
        populator.db_conn.close()
        return True
        
    except Exception as e:
        logging.error(f"Single timeframe population test failed: {e}")
        import traceback
        logging.error(f"Traceback: {traceback.format_exc()}")
        return False

def check_existing_data():
    """Check existing data in timeframe tables."""
    logging.info("--- Checking Existing Data ---")
    
    config = utils.get_config()
    
    try:
        db_conn = psycopg2.connect(
            host=config.db_host,
            database=config.db_name,
            user=config.db_user,
            port=config.db_port
        )
        
        tables = ['ohlc_5min', 'ohlc_15min', 'ohlc_1hour', 'ohlc_daily']
        
        for table in tables:
            with db_conn.cursor() as cur:
                cur.execute(f"SELECT COUNT(*) FROM {table}")
                count = cur.fetchone()[0]
                
                if count > 0:
                    cur.execute(f"SELECT MAX(time), MIN(time) FROM {table}")
                    max_time, min_time = cur.fetchone()
                    
                    cur.execute(f"SELECT COUNT(DISTINCT symbol) FROM {table}")
                    symbol_count = cur.fetchone()[0]
                    
                    logging.info(f"{table}: {count} records, {symbol_count} symbols, {min_time} to {max_time}")
                else:
                    logging.info(f"{table}: No data")
        
        db_conn.close()
        return True
        
    except Exception as e:
        logging.error(f"Error checking existing data: {e}")
        return False

def main():
    """Run all tests."""
    logging.info("Starting cron_ohlc_timeframes.py tests...")
    
    tests = [
        ("Business Hours Detection", test_business_hours),
        ("Database Connection", test_database_connection),
        ("Timeframe Logic", test_timeframe_logic),
        ("Existing Data Check", check_existing_data),
        ("Single Timeframe Population", test_single_timeframe_population),
    ]
    
    results = {}
    for test_name, test_func in tests:
        logging.info(f"\n{'='*50}")
        logging.info(f"Running: {test_name}")
        logging.info('='*50)
        
        try:
            results[test_name] = test_func()
        except Exception as e:
            logging.error(f"Test {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    logging.info(f"\n{'='*50}")
    logging.info("TEST RESULTS SUMMARY")
    logging.info('='*50)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        logging.info(f"{test_name}: {status}")
    
    all_passed = all(results.values())
    if all_passed:
        logging.info("\n🎉 All tests passed! Ready to run cron_ohlc_timeframes.py")
    else:
        logging.error("\n⚠️  Some tests failed. Please fix issues before running the cron script.")

if __name__ == "__main__":
    main()
