import os
import threading
from datetime import datetime, timedelta
import utils
from telegram_utils import TelegramUtil
from broker import Zerodha
import time
import psycopg2
import psycopg2.pool
import logging
from multiprocessing import Process
import urllib.request
from broker_singleton import Broker<PERSON><PERSON><PERSON>
from unusual_option_activity import UnusualOptionActivity
from filtered_instruments import FilteredInstruments

logging.basicConfig(level=logging.INFO,
                    format="%(asctime)s %(levelname)s: %(message)s",
                    datefmt="%Y-%m-%d %H:%M:%S")



class OHLCVWriter:
    def __init__(self):
        self.config = utils.get_config()
        self.broker = BrokerSingleton()

        self.insert_query = f"""
            INSERT INTO ohlc_1min
            (time, symbol, price, volume, oi)
            VALUES (%s, %s, %s, %s, %s)
        """

    def save_quote_to_database(self, conn, quotes):
        with conn.cursor() as cur:
            cur.executemany(self.insert_query, [(q['time'], q['symbol'], q['price'], q['volume'],  q['oi']) for q in quotes])
            conn.commit()


    def run(self, instrument_token_list, df):
        from croniter import croniter
        from datetime import datetime
        import time
        db_conn = psycopg2.connect(
            host=self.config.db_host,
            database=self.config.db_name,
            user=self.config.db_user,
            port=self.config.db_port
        )
        start_time = datetime.strptime('09:15:00', '%H:%M:%S').time()
        end_time = datetime.strptime('15:31:00', '%H:%M:%S').time()
        prod_run = True

        schedule = "* * * * *"

        cron = croniter(schedule, datetime.now())
        df.set_index('instrument_token', inplace=True)

        def update_day_volume_and_return_ohlc_data():
            quotes = self.broker.get_quote(instrument_token_list)
            ohlc_data = [
                {
                    'time': datetime.now().replace(second=0, microsecond=0), 
                    'symbol': df.loc[v['instrument_token']]['tradingsymbol'], 
                    'price': v['last_price'], 
                    'volume': int(v['volume'] - df.loc[v['instrument_token']]['day_volume']), 
                    'oi': int(v['oi'])
                }
                for _, v in quotes.items()
            ]
            for _, v in quotes.items():
                df.loc[v['instrument_token'], 'day_volume'] = v['volume']
            return ohlc_data

        def task():
            ohlc_data = update_day_volume_and_return_ohlc_data()
            self.save_quote_to_database(db_conn, ohlc_data)
            print(datetime.now(), ": Iteration completed for timestamp: ", datetime.now().replace(second=0, microsecond=0), "thread_id:", threading.get_ident())

        update_day_volume_and_return_ohlc_data()
        while True:
            current_time = datetime.now().time()
            if not (start_time <= current_time < end_time) and prod_run:
                print("Execution completed or outside expected time range")
                break
            else:
                next_execution = next_execution = cron.get_next(datetime)
                # next_execution = cron.get_next(datetime)
            time.sleep((next_execution - datetime.now()).total_seconds())
            task()

muv = OHLCVWriter()

from multiprocessing.pool import ThreadPool

def run_muv(part, df):
    muv.run(part, df)

if __name__ == "__main__":
    print(__file__)
    time.sleep(30)
    try:
        urllib.request.urlretrieve('https://api.kite.trade/instruments', 'zerodha_instruments.csv')
    except:
        pass
    fi = FilteredInstruments(muv.broker)

    instruments = fi.df['instrument_token'].to_list()
    partitions = []
    num_partitions = len(instruments)//1000
    for i in range(num_partitions):
        partitions.append(instruments[i*1000:i*1000+1000])
    partitions.append(instruments[num_partitions*1000:])

    # Create a ThreadPool with the number of partitions
    pool = ThreadPool(len(partitions))

    # Use the ThreadPool to run the tasks
    pool.starmap(run_muv, [(part, fi.df.copy()) for part in partitions])

    # Close the pool and wait for all tasks to finish
    pool.close()
    pool.join()
