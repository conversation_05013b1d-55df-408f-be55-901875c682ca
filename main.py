# from concurrent.futures import thread
# from tweet_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>
# from twitter_util import TwitterStreamingClient
import utils
import pdb
import yaml
import constants
from config import Config
import time
from position import Position
from kiteconnect_extras import KiteExt
import logging
import os
from broker import Zerodha
# from broker import Kotak
# from broker import KotakA<PERSON>unt

# from broker import Upstox
from strategy import RSIBuyStrategy
from strategy import RSISellStrategy
# import django
import sys
from datetime import datetime
import pytz
IST = pytz.timezone('Asia/Kolkata')
from algorithm import Algo
import threading
import asyncio
from telegram_msg_handler import TelegramMsgHandler
from trade_observer import TradeObserver
import traceback
import signal
import sys
signal.signal(signal.SIGINT, lambda x, y: sys.exit(0))

# BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
# sys.path.append(BASE_DIR)
# os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'proj.settings')
# django.setup()
from telegram_utils import TelegramUtil
from telethon import events

logging.basicConfig(level=logging.ERROR, format='%(asctime)s - %(message)s', datefmt='%d-%b-%y %H:%M:%S')

async def factorial(name, number):
    f = 1
    for i in range(2, number + 1):
        print(f"Task {name}: Compute factorial({number}), currently i={i}...")
        await asyncio.sleep(1)
        f *= i
    print(f"Task {name}: factorial({number}) = {f}")
    return f



# async def main():
if __name__ == "__main__":
    config = utils.get_config()
    # print(config)
    fno_lot_data = utils.get_fno_lot_data()
    tutil = TelegramUtil(config)
    telegram_client = tutil.client
    telegram_bot = tutil.bot
    zerodha = Zerodha(
        config.zerodha_username,
        api_key=config.zerodha_api_key,
        access_token=config.zerodha_access_token
    )
    # balance = broker.get_margin()['available']['live_balance']
    # print("balance: ", balance)
    # kotak_account = KotakAccount(config.kotak_userid, config.kotak_pass, config.kotak_access_token, config.kotak_consumer_key, config.kotak_consumer_secret)
    # kotak = Kotak(kotak_account=kotak_account, parent=True)

    algo = Algo(config, zerodha, None, telegram_bot)
    # print(algo.broker.positions())
    # algo.run(instrument_token_list=[********])
    # exit
    trade_observer = TradeObserver(algo, fno_lot_data)
    telegram_msg_handler = TelegramMsgHandler(trade_observer)
    # tweet_handler = TweetHandler(trade_observer=trade_observer, telegram_client=telegram_client, twitter_recos_to_telegram_channel=config.twitter_recos_to_telegram_channel)

    @telegram_client.on(events.NewMessage(chats=config.telegram_recommendation_channels))
    async def my_event_handler(event):
        try:
            await telegram_msg_handler.telegram_handler(event, telegram_bot)
        except Exception as e:
            traceback.print_exc()

    # t = TwitterStreamingClient(config=config, telegram_client=telegram_client, tweet_handler=tweet_handler)
    loop = asyncio.get_event_loop()
    # loop.create_task(t.run())
    loop.create_task(telegram_client.run_until_disconnected())
    loop.run_forever()
    print("event_loop_running")




    # q = algo.broker.get_quote('NFO:BANKNIFTY22JAN38000CE')
    # print(q)

    # buy_string = '34400CE'
    # tradingsymbol = utils.next_expiry_prefix('BANKNIFTY')+buy_string
    # print(tradingsymbol)
    # buy_price = 280
    # exit_price = 800
    # algo.broker.place_order(tradingsymbol=tradingsymbol, transaction_type=Literal.BUY, quantity=25, product='MIS', order_type='LIMIT', price=buy_price)
    # algo.broker.place_order(tradingsymbol=tradingsymbol, transaction_type=Literal.SELL, quantity=25, product='MIS', order_type='SL', price=buy_price, trigger_price=buy_price)
    # algo.broker.place_order(tradingsymbol=tradingsymbol, transaction_type=Literal.SELL, quantity=25, product='MIS', order_type='LIMIT', price=exit_price)

    # p = algo.broker.positions()
    # p = algo.open_positions()
    # print(p)
    # rbs = RSIBuyStrategy()
    # rss = RSISellStrategy()
    # print(rbs.is_buy(q))
    # print(rss.is_sell(q))
    # trade = Trade(symbol=algo.config.index_fut,
    #                   quantity=algo.config.quantity, exchange=Literal.NFO, transaction_type=Literal.SELL, sl=algo.config.sl/2)
    # print(trade.__dict__)
    # print(trade.get_absolute_sl_trigger_price(q.last_price))
    # algo.place_order(trade)
    print(algo.broker.positions())
    # algo.broker.place_gtt(tradingsymbol=tradingsymbol, quantity=25, curr_price=640.05, sl_price=630.0, target_price=650.0)
    print(algo.broker.get_gtts())
    # print(algo.broker.orders())
    # print("[instruments]: ", algo.save_instruments())
    # pdb.set_trace()
    q = algo.get_quote('BANKNIFTY2160335100', 'NFO')
    # q = algo.kite.quote('NFO:BANKNIFTY2160335100CE')
    # print(q)
    # cmp = q.last_price
    # algo.run([constants.INSTRUMENT_BANKNIFTY_INDEX])
    # pdb.set_trace()

# asyncio.run(main())
