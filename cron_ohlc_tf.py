#!/usr/bin/env python3
"""
<PERSON>ron script to populate OHLC timeframe tables (5min, 15min, 1hour, daily) from ohlc_1min data.
Runs every 5 minutes and intelligently determines which timeframes need to be populated.

Schedule:
- ohlc_5min: Every 5 minutes (9:20, 9:25, 9:30, etc.)
- ohlc_15min: Every 15 minutes (9:30, 9:45, 10:00, etc.)
- ohlc_1hour: Every hour (10:30, 11:30, 12:30, etc.)
- ohlc_daily: Once daily at 3:30 PM
"""

from croniter import croniter
from datetime import datetime, time as dt_time, timedelta
import time
import psycopg2
import utils
import logging
from filtered_instruments import FilteredInstruments
from broker_singleton import BrokerSingleton

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s %(levelname)s: %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S"
)

def is_outside_business_hours():
    """Check if current time is outside business hours."""
    now = datetime.now()
    if now.weekday() in [5, 6]:  # Weekend
        return True
    start_time = dt_time(hour=9, minute=15)
    end_time = dt_time(hour=15, minute=35)
    return not (start_time <= now.time() <= end_time)

class OHLCTimeframePopulator:
    def __init__(self):
        """Initialize with database connection and NSE F&O stocks."""
        self.config = utils.get_config()
        self.db_conn = psycopg2.connect(
            host=self.config.db_host,
            database=self.config.db_name,
            user=self.config.db_user,
            port=self.config.db_port
        )

        # Get NSE F&O stocks from FilteredInstruments
        logging.info("Initializing FilteredInstruments...")
        self.broker = BrokerSingleton()
        self.filtered_instruments = FilteredInstruments(self.broker)
        self.nse_stocks = self.filtered_instruments.nse_stocks['name'].tolist()

        logging.info(f"Initialized with {len(self.nse_stocks)} NSE F&O stocks")

    def should_populate_timeframe(self, current_time, timeframe):
        """Determine if a timeframe should be populated based on current time."""
        minute = current_time.minute
        hour = current_time.hour

        if timeframe == '5min':
            # Every 5 minutes: 9:20, 9:25, 9:30, etc.
            return minute % 5 == 0

        elif timeframe == '15min':
            # Every 15 minutes: 9:30, 9:45, 10:00, etc.
            return minute % 15 == 0

        elif timeframe == '1hour':
            # Every hour at 30 minutes: 10:30, 11:30, 12:30, etc.
            return minute == 30

        elif timeframe == 'daily':
            # Once daily at 3:30 PM
            return hour == 15 and minute == 30

        return False

    def get_time_bucket(self, current_time, timeframe):
        """Get the time bucket for aggregation based on timeframe."""
        if timeframe == '5min':
            # Round down to nearest 5-minute boundary
            bucket_minute = (current_time.minute // 5) * 5
            return current_time.replace(minute=bucket_minute, second=0, microsecond=0)

        elif timeframe == '15min':
            # Round down to nearest 15-minute boundary
            bucket_minute = (current_time.minute // 15) * 15
            return current_time.replace(minute=bucket_minute, second=0, microsecond=0)

        elif timeframe == '1hour':
            # Hour bucket from 30 minutes ago to now (e.g., 9:30-10:30)
            return current_time.replace(minute=30, second=0, microsecond=0)

        elif timeframe == 'daily':
            # Daily bucket for the entire trading day
            return current_time.replace(hour=9, minute=15, second=0, microsecond=0)

    def populate_timeframe_for_symbols(self, timeframe, current_time):
        """Populate a specific timeframe for all symbols using optimized SQL."""
        table_name = f"ohlc_{timeframe}"
        bucket_time = self.get_time_bucket(current_time, timeframe)

        logging.info(f"Populating {table_name} for bucket {bucket_time}")

        # Define time range for aggregation - use previous time bucket
        if timeframe == '5min':
            end_time = bucket_time
            start_time = end_time - timedelta(minutes=5)
        elif timeframe == '15min':
            end_time = bucket_time
            start_time = end_time - timedelta(minutes=15)
        elif timeframe == '1hour':
            end_time = bucket_time
            start_time = end_time - timedelta(hours=1)
        elif timeframe == 'daily':
            end_time = current_time  # Current time (should be 3:30 PM)
            start_time = bucket_time  # 9:15 AM (market open)

        # Simple and reliable SQL query for aggregation
        aggregate_query = f"""
            INSERT INTO {table_name} (time, symbol, price, volume, oi)
            SELECT
                %s as time,
                symbol,
                (array_agg(price ORDER BY time DESC))[1] as price,  -- Last price
                SUM(volume) as volume,  -- Sum of volumes
                (array_agg(oi ORDER BY time DESC))[1] as oi  -- Last OI
            FROM ohlc_1min
            WHERE symbol = ANY(%s)
            AND time >= %s AND time < %s
            GROUP BY symbol
            HAVING COUNT(*) > 0
            ON CONFLICT (symbol, time) DO UPDATE SET
                price = EXCLUDED.price,
                volume = EXCLUDED.volume,
                oi = EXCLUDED.oi
        """

        try:
            with self.db_conn.cursor() as cur:
                cur.execute(aggregate_query, (
                    bucket_time,      # time for INSERT
                    self.nse_stocks,  # symbol filter
                    start_time,       # start time range
                    end_time          # end time range
                ))
                rows_affected = cur.rowcount
                self.db_conn.commit()

            logging.info(f"Successfully populated {rows_affected} records in {table_name}")

        except Exception as e:
            logging.error(f"Error populating {table_name}: {e}")
            self.db_conn.rollback()

populator = OHLCTimeframePopulator()

def task(current_time):
    """Main task function that runs every 5 minutes."""
    if is_outside_business_hours():
        logging.info("Outside business hours, skipping...")
        return

    # current_time = datetime.now()
    logging.info(f"Running OHLC timeframe population task at {current_time}")

    try:
        # Check which timeframes need to be populated
        timeframes_to_populate = []

        for timeframe in ['5min', '15min', '1hour', 'daily']:
            if populator.should_populate_timeframe(current_time, timeframe):
                timeframes_to_populate.append(timeframe)

        if not timeframes_to_populate:
            logging.info("No timeframes need to be populated at this time")
            return

        logging.info(f"Populating timeframes: {timeframes_to_populate}")

        # Populate each required timeframe
        for timeframe in timeframes_to_populate:
            populator.populate_timeframe_for_symbols(timeframe, current_time)

        logging.info("Task completed successfully")

    except Exception as e:
        logging.error(f"Error in task execution: {e}")
        import traceback
        logging.error(f"Traceback: {traceback.format_exc()}")

    finally:
        if 'populator' in locals():
            populator.db_conn.close()

# Cron schedule: every 5 minutes
schedule = "*/5 * * * *"
cron = croniter(schedule, datetime.now())

logging.info(f"Starting OHLC timeframe populator: {__file__}")
logging.info("Schedule: Every 5 minutes during business hours")

while True:
    next_execution = cron.get_next(datetime)
    sleep_seconds = (next_execution - datetime.now()).total_seconds()+30

    logging.info(f"Next execution at {next_execution}, sleeping for {sleep_seconds:.1f} seconds")
    time.sleep(sleep_seconds)

    task(next_execution)
