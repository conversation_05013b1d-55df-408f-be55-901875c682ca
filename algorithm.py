from config import Config
from broker.interfaces import BrokerI
from constants import Literal
import utils
from quote import Quote
from trade import Trade
import logging
from position import Position
import calendar
from datetime import datetime
import pytz
IST = pytz.timezone('Asia/Kolkata')
import constants
# from models import Tick, Instrument
from PIL import Image
import pytesseract
import re
import os
import queue

class Algo:
    def __init__(self, config: Config, broker: BrokerI, broker2: BrokerI, telegram_bot: None):
        self.broker = broker
        self.broker2 = broker2
        self.config = config
        self.next_trade_type = Literal.LONG
        self.next_expiry_prefix = utils.next_expiry_prefix
        self.sl_trigger_price = None
        self.previous_close = 0.0
        self.telegram_buy_orders = []
        self.instrument_token_list = None
        self.telegram_bot = telegram_bot
        self.q = q = queue.Queue()
    # def get_broker(self):
    #     kite = KiteExt()
    #     kite.set_headers(self.config.enctoken, userid=self.config.username)
    # return kite

    def open_positions(self):
        from position import Position
        return [Position(i) for i in self.broker.positions()['net'] if abs(i['quantity']) > 0]

    def get_quote(self, symbol: str, exchange: str) -> Quote:
        # pdb.set_trace()
        return Quote(symbol, self.broker.get_quote([exchange + ":" + symbol])[exchange + ":" + symbol])

    def place_order(self, trade: Trade):
        order_id = self.broker.place_order(tradingsymbol=trade.symbol, transaction_type=trade.transaction_type,
                                           quantity=trade.quantity, product='MIS', order_type='MARKET',
                                           trigger_price=self.sl_trigger_price)
        trade.set_executed()
        logging.info("Order placed: {}".format(trade))

    def get_curr_bn_position(self) -> Position:
        open_bn_positions = [p for p in self.open_positions() if p.symbol.startswith(
            Literal.BANKNIFTY)]

        return open_bn_positions[0] if len(open_bn_positions) > 0 else None

    def update_cover_order(self, symbol, new_sl_trigger_price):
        kite_orders = self.kite.orders()
        order_id = [o for o in kite_orders if o['status'] ==
                    'TRIGGER PENDING' and o['tradingsymbol'] == symbol][0]['order_id']
        self.kite.modify_order(order_id=order_id, trigger_price=new_sl_trigger_price, variety='co')
        logging.info("Order updated: {}".format(symbol))

    def get_itm_symbol(self, banknifty_index_cmp, strategy_type):
        if strategy_type == Literal.LONG:
            return self.next_expiry_prefix + utils.rounddown(banknifty_index_cmp) + Literal.CE
        if strategy_type == Literal.SHORT:
            return self.next_expiry_prefix + utils.roundup(banknifty_index_cmp) + Literal.PE

    def save_ticks(self, ticks):
        for t in ticks:
            print(t.__dict__)
            self.q.put(t)
        # for t in ticks:
        #     diff = round(t['last_price']-self.previous_close, 2)
        #     if abs(diff) > 2 or True:
        #         print(datetime.now(IST).strftime("%H:%M:%S"), t['last_price'], "diff: ", str(diff))
            # Tick.objects.create(symbol=str(t['instrument_token']), cmp=t['last_price'])

    def place_applicable_orders(self):
        self.buy_strategy.place_orders()
        pass

    def close_applicable_orders(self):
        self.sell_strategy.close_orders()
        pass

    def on_ticks(self, ws, ticks):
        # Callback to receive ticks.
        # logging.info("Ticks: {}".format(ticks))
        # if ticks[0]['last_price'] > 37650.0:
        #     ws.subscribe([********])
        #     ws.unsubscribe([********])
        # else:
        #     ws.subscribe(([********]))
        #     ws.unsubscribe([********])
        self.save_ticks(ticks)
        # self.previous_close = ticks[0]['last_price']

        # self.close_applicable_orders()
        # self.place_applicable_orders()

        # new_close = ticks[0]['last_price']
        # if self.previous_close:
        #     diff = new_close - self.previous_close
        #     if abs(diff) > 0.0:
        #         logging.info("diff: " + str(diff))
        # self.previous_close = new_close
        # return
        # curr_bn_position = self.get_curr_bn_position()

        return
        if curr_bn_position is None:
            bn_index_cmp = self.get_quote('NIFTY BANK', Literal.NSE).last_price
            print(self.config.__dict__)
            if not self.config.index_fut:
                symbol = self.get_itm_symbol(bn_index_cmp, self.next_trade_type)
            else:
                symbol = self.config.index_fut
            if not self.config.index_fut:
                transaction_type = Literal.BUY
            else:
                if self.next_trade_type == Literal.LONG:
                    transaction_type = Literal.BUY
                else:
                    transaction_type = Literal.SELL
            t = Trade(symbol=symbol,
                      quantity=self.config.quantity, exchange=Literal.NFO, transaction_type=transaction_type,
                      sl=self.config.sl / 2)
            q = self.get_quote(t.symbol, t.exchange)
            self.sl_trigger_price = t.get_absolute_sl_trigger_price(q.last_price)
            self.place_order(t)
            self.next_trade_type = Literal.SHORT if self.next_option_type == Literal.LONG else Literal.LONG
        else:
            bn_pos_cmp = self.get_quote(
                curr_bn_position.symbol, curr_bn_position.exchange).last_price

            if bn_pos_cmp > self.sl_trigger_price + 1.5 * self.config.sl:
                new_sl_trigger_price = self.sl_trigger_price + self.config.sl
                self.update_cover_order(curr_bn_position.symbol,
                                        new_sl_trigger_price)
                self.sl_trigger_price = new_sl_trigger_price

    def on_connect(self, ws, response):
        # Callback on successful connect.
        # Subscribe to a list of instrument_tokens (BANKNIFTY and NIFTY here).

        ws.subscribe(self.instrument_token_list)
        # Set to tick in `ltp` mode.
        ws.set_mode(ws.MODE_LTP, self.instrument_token_list)

    def on_error(self, ws, code, reason):
        # On connection close stop the main loop
        # Reconnection will not happen after executing `ws.stop()`
        print(code, reason)

    def on_close(self, ws, code, reason):
        # On connection close stop the main loop
        # Reconnection will not happen after executing `ws.stop()`
        print(code, reason)
        # ws.stop()

    def run(self, instrument_token_list=None):
        self.instrument_token_list = instrument_token_list
        # while True:
        #     self.on_ticks(None, None)
        #     time.sleep(2)
        kws = self.broker.ticker()
        # Assign the callbacks.
        kws.on_ticks = self.on_ticks
        kws.on_connect = self.on_connect
        kws.on_close = self.on_close
        kws.on_error = self.on_error
        kws.connect()

    def save_instruments(self, exchange=None):
        return self.broker.instruments(exchange=exchange)
        # instruments = [Instrument(symbol=i['tradingsymbol'], instrument_token=i['instrument_token'])
        #                for i in instruments if i['name'] in
        #                ['BANKNIFTY', 'NIFTY BANK'] and i['segment'] in ['NFO-OPT', 'INDICES']]
        # Instrument.objects.all().delete()
        # Instrument.objects.bulk_create(instruments)

    # def get_symbol_prefix(self, strike_price):
    #     if strike_price > 28000:
    #         return 'BANKNIFTY'
    #     else:
    #         return 'NIFTY'

    def get_order_qty(self, symbol, price=None, is_confident=False):
        def get_order_qty_bn(premium):
            return min(int((self.config.capital/premium)/25)*25, 48*25) if is_confident else self.config.quantity * 25

        def get_order_qty_nifty(premium):
            return min(int((self.config.capital/premium)/50)*50, 56*50) if is_confident else self.config.quantity * 50

        if symbol.startswith('BANKNIFTY'):
            return get_order_qty_bn(price)
        elif symbol.startswith('NIFTY'):
            return get_order_qty_nifty(price)
        else:
            return self.config.quantity


