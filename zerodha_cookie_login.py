import pyautogui
import os
screenWidth, screenHeight = pyautogui.size() # Get the size of the primary monitor.
pyautogui.FAILSAFE = True
pyautogui.PAUSE = 0.5
import yaml

with open('/Users/<USER>/algotrading/scalping/config.yml', 'r') as file:
    data = yaml.safe_load(file)
    enctoken = data['zerodha_enctoken']
    public_token = data['zerodha_public_token']
    kf_session = data['zerodha_kf_session']
    user_id = data['zerodha_username']

def start_login_flow():
    pyautogui.click(240, 878) # Open google chrome
    pyautogui.click(521, 87) # Click on address bar
    pyautogui.write('https://kite.zerodha.com')
    pyautogui.press('enter')
    pyautogui.press('f12')
    pyautogui.click(707, 455) # Click on Application in chrome console
    pyautogui.click(124, 690) # Click on cookies section
    pyautogui.press('right') # Open zerodha cookies
    pyautogui.rightClick(112, 710) # Right click to delete existing zerodha cookies
    pyautogui.click(143, 721) # Delete existing cookies

    # Go to add 1st cookie
    pyautogui.click(264, 532)
    pyautogui.press('enter')
    pyautogui.keyUp('Fn'); pyautogui.write('enctoken')
    pyautogui.press('enter')
    pyautogui.keyUp('Fn'); pyautogui.write(enctoken)
    pyautogui.press('enter')

    # # Go to add 2nd cookie
    pyautogui.click(264, 552)
    pyautogui.press('enter')
    pyautogui.keyUp('Fn'); pyautogui.write('public_token')
    pyautogui.press('enter')
    pyautogui.keyUp('Fn'); pyautogui.write(public_token)
    pyautogui.press('enter')

    # # Go to add 3rd cookie
    pyautogui.click(264, 572)
    pyautogui.press('enter')
    pyautogui.keyUp('Fn'); pyautogui.write('kf_session')
    pyautogui.press('enter')
    pyautogui.keyUp('Fn'); pyautogui.write(kf_session)
    pyautogui.press('enter')


    # # Go to add 4th cookie
    pyautogui.click(264, 592)
    pyautogui.press('enter')
    pyautogui.keyUp('Fn'); pyautogui.write('user_id')
    pyautogui.press('enter')
    pyautogui.keyUp('Fn'); pyautogui.write(user_id)
    pyautogui.press('enter')

    # Reload the page now
    pyautogui.click(713, 354)
    pyautogui.hotkey('command', 'r')

    # Close console    
    pyautogui.press('f12')

    # Close restore page popup
    pyautogui.click(1423, 117)

# os.system("killall -9 'Google Chrome'")

start_login_flow()
