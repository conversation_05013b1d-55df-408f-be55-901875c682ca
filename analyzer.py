import calendar
import psycopg2
import utils
import datetime
import warnings
import pandas as pd
import numpy as np
import math

pd.options.mode.copy_on_write = True

# warnings.simplefilter(action='ignore', category=FutureWarning)
import pandas

class DataAnalyzer:
    def __init__(self) -> None:
        config = utils.get_config()
        self.db_conn = psycopg2.connect(
            host=config.db_host,
            database=config.db_name,
            user=config.db_user,
            port=config.db_port
        )
        self.df_all_stocks = pd.read_pickle('2024/fno_stocks_1min_data/all_stocks.pkl')
        self.df_all_stocks.rename(columns={'ticker': 'spot_symbol', 'date': 'time', 'close': 'spot_price'}, inplace=True)
        self.df_all_stocks['time'] = pd.to_datetime(self.df_all_stocks['time'], format='%Y-%m-%d %H:%M:%S%z')

    def get_trading_days(self, year, month, date_starting_from=None):
        # Get all the dates in a given month and year
        def get_last_thursday(year, month):
            cal = calendar.monthcalendar(year, month)
            last_week = cal[-1]
            if last_week[calendar.THURSDAY] != 0:
                last_thursday = last_week[calendar.THURSDAY]
            else:
                last_thursday = cal[-2][calendar.THURSDAY]
            return last_thursday

        dates = []
        num_days = calendar.monthrange(year, month)[1]
        for day in range(1, num_days + 1):
            date = datetime.date(year, month, day)
            dates.append(date)
        holidays = ['2023-01-26', '2023-03-07', '2023-03-30', '2023-04-04', '2023-04-07', '2023-04-14', '2023-05-01', '2023-06-29', '2023-08-15', '2023-09-19', '2023-10-02', '2023-10-24', '2023-11-14', '2023-11-27', '2023-12-25', '2024-01-22', '2024-01-26', '2024-03-08', '2024-03-25', '2024-03-29', '2024-04-11', '2024-04-17', '2024-05-01', '2024-06-17', '2024-07-17', '2024-08-15', '2024-10-02', '2024-11-01', '2024-11-15', '2024-12-25']
        trading_days = [d for d in dates if d.weekday() < 5 and str(d) not in holidays]
        if date_starting_from:
            trading_days = [t for t in trading_days if t.day>=date_starting_from]
        last_thursday = get_last_thursday(year, month)
        trading_days = [t for t in trading_days if t.day<=last_thursday]
        return trading_days


    def get_transformed_df(self, day, month, year):
        month_name = calendar.month_abbr[month].upper()
        df_file_name = f'/Users/<USER>/Downloads/2024/GFDLNFO_BACKADJUSTED_{day:02d}{month:02d}{year:04d}.pkl'
        df = pd.read_pickle(df_file_name)
        df['time'] = pd.to_datetime(df['Date'] + ' ' + df['Time']+'+05:30', format='%d/%m/%Y %H:%M:%S%z')
        df['time'] = df['time'] - pd.Timedelta(seconds=59)
        df.rename(columns={'Ticker': 'symbol', 'Close': 'price', 'High': 'high', 'Low': 'low', 'Volume': 'volume', 'Open Interest': 'oi'}, inplace=True)
        df['symbol'] = df['symbol'].str[:-4]
        df['symbol'] = df['symbol'].str.replace(r'(\d{2})(\w{3})(\d{2})', r'\3\2', regex=True)
        df = df.sort_values(['time', 'symbol'])
        df = df[df['symbol'].str.endswith(('CE', 'PE'))]
        df = df[df['symbol'].str.contains(month_name)] # Filter useless symbols
        df['spot_symbol'] = df['symbol'].str.extract(r'^([A-Za-z&-]+)')
        df['ce_pe'] = df['symbol'].str[-2:]
        df['strike_price'] = df['symbol'].str.extract(r'(\d+\.?\d*)(?=[CE|PE])').astype(float)
        df = df[~df['symbol'].str.contains('NIFTY')]
        df = df.merge(self.df_all_stocks, on=['time', 'spot_symbol'], how='left')
        df.rename(columns={'volume_x': 'volume', 'high_x': 'high', 'low_x': 'low'}, inplace=True)
        df = df[['time', 'symbol', 'spot_symbol', 'price', 'high', 'low', 'volume', 'oi', 'spot_price']]
        df = df[df['spot_price'].notnull()]
        df['ce_pe'] = df['symbol'].str[-2:]
        df['strike_price'] = df['symbol'].str.extract(r'(\d+\.?\d*)(?=[CE|PE])').astype(float)
        df['traded_value'] = df['price'] * df['volume']
        return df

    def pre_filtering(self, df):
        return df[~((df['ce_pe'] == 'PE') & (df['strike_price'] > 0.99 * df['spot_price']) | (df['ce_pe'] == 'CE') & (df['strike_price'] < 1.01 * df['spot_price']))]

    def post_strategy(self, df):

        # current_ce_pe_counts = df.groupby(['spot_symbol', 'time', 'ce_pe']).size().unstack(fill_value=0)
        # current_ce_pe_counts.columns = ['current_ce_count', 'current_pe_count']

        current_ce_counts = df[df['ce_pe'] == 'CE'].groupby(['spot_symbol', 'time', 'ce_pe']).size().unstack(fill_value=0).rename(columns={'CE': 'current_ce_count'})
        current_pe_counts = df[df['ce_pe'] == 'PE'].groupby(['spot_symbol', 'time', 'ce_pe']).size().unstack(fill_value=0).rename(columns={'PE': 'current_pe_count'})
        current_ce_pe_counts = current_ce_counts.merge(current_pe_counts, on=['spot_symbol', 'time'], how='outer').fillna(0)

        if 'current_ce_count' not in current_ce_pe_counts.columns:
            current_ce_pe_counts['current_ce_count'] = 0
        if 'current_pe_count' not in current_ce_pe_counts.columns:
            current_ce_pe_counts['current_pe_count'] = 0
        # print(current_ce_counts)
        # print(current_pe_counts)

        current_ce_pe_counts['cumulative_ce_count'] = current_ce_pe_counts.groupby('spot_symbol')['current_ce_count'].cumsum()
        current_ce_pe_counts['cumulative_pe_count'] = current_ce_pe_counts.groupby('spot_symbol')['current_pe_count'].cumsum()

        # print(current_ce_pe_counts)

        df = df.merge(current_ce_pe_counts, on=['spot_symbol', 'time'], how='left')
        df = df[((df['current_ce_count']>=3) & (df['cumulative_ce_count']<=6) & (df['cumulative_pe_count']==0)) | ((df['current_pe_count']>=3) & (df['cumulative_pe_count']<=6) & (df['cumulative_ce_count']==0))]
        return df

    def clean_duplicated(self, df1):
        df_ces = df1[df1['ce_pe'] == 'CE'].groupby(['time', 'spot_symbol'])['strike_price'].min().reset_index()
        df_ces = df_ces.merge(df1, on=['time', 'spot_symbol', 'strike_price'], how='left')
        df_pes = df1[df1['ce_pe'] == 'PE'].groupby(['time', 'spot_symbol'])['strike_price'].max().reset_index()
        df_pes = df_pes.merge(df1, on=['time', 'spot_symbol', 'strike_price'], how='left')
        df_buys = pd.concat([df_ces, df_pes]).sort_values(by=['time', 'symbol'])
        df_buys = df_buys.sort_values(by='time').drop_duplicates(subset='spot_symbol', keep='first')
        return df_buys

    def get_success(self, df_buys, df, success_multiplier=1.1):
        df = df.sort_values(by='time')
        df['is_buy'] = np.nan
        df['sell_time'] = np.nan
        df['sell_price'] = np.nan
        for _, row in df_buys.iterrows():
            df.loc[(df['time'] == row['time']) & (df['symbol'] == row['symbol']), 'is_buy'] = True

        for _, row in df_buys.iterrows():
            index_in_df = df[(df['time'] == row['time']) & (df['symbol'] == row['symbol'])].index
            df.loc[index_in_df, 'is_buy'] = True
            for _, next_row in df[(df['symbol'] == row['symbol']) & (df['time']>row['time'])].iterrows():
                if next_row['high'] >= row['price'] * success_multiplier:
                    df.loc[index_in_df, 'sell_time'] = next_row['time']
                    df.loc[index_in_df, 'sell_price'] = next_row['price']
                    break
                elif next_row['low'] <= row['price']*0.9:
                    df.loc[index_in_df, 'sell_time'] = next_row['time']
                    df.loc[index_in_df, 'sell_price'] = next_row['price']
                    break

        df = df[df['is_buy']==True]
        df = df[['time', 'symbol', 'price', 'sell_price', 'sell_time']]
        return df

    def success_rate(self, df):
        percentage = (df['sell_price'] > df['price']).mean()
        if pd.isnull(percentage):
            percentage = 0
        return percentage

    # Consecutive 3 candles with more than 5% increase
    def get_df_passing_strategy_3_consecutive_incr_5_perc(self, df):
        df_filtered = df
        df_filtered['prev_price'] = df_filtered.groupby('symbol')['price'].shift()
        df_filtered['price_incr_5pc'] = df_filtered['price'] > 1.05 * df_filtered['prev_price']
        df_filtered['prev_prev_price'] = df_filtered.groupby('symbol')['prev_price'].shift()
        df_filtered['prev_price_incr_5pc'] = df_filtered['prev_price'] > 1.05 * df_filtered['prev_prev_price']
        df_filtered['prev_prev_prev_price'] = df_filtered.groupby('symbol')['prev_prev_price'].shift()
        df_filtered['prev_prev_price_incr_5pc'] = df_filtered['prev_prev_price'] > 1.05 * df_filtered['prev_prev_prev_price']

        df_filtered = df_filtered[df_filtered['price_incr_5pc'] & df_filtered['prev_price_incr_5pc'] & df_filtered['prev_prev_price_incr_5pc']]
        return df_filtered

    # Traded value > 500000
    def get_df_passing_strategy_traded_val_gt_500000(self, df):
        df['prev_price'] = df.groupby('symbol')['price'].shift()
        df = df[(df['price']>df['prev_price']*1.01) & (df['traded_value']>=500000)]
        return df

    # Traded value > 1000000
    def get_df_passing_strategy_traded_val_gt_1000000(self, df):
        df['prev_price'] = df.groupby('symbol')['price'].shift()
        df = df[(df['price']>df['prev_price']*1.01) & (df['traded_value']>=1000000)]
        return df

    # Candle delta > 50000 and price_incr more than 1%
    def get_df_passing_strategy_candle_delta_gt_50000_and_price_incr_1_perc(self, df):
        df['prev_price'] = df.groupby('symbol')['price'].shift()
        df = df[(df['price']>df['prev_price']*1.01) & ((df['price']-df['prev_price'])*df['volume']>=50000)]
        return df

    # Candle delta > 50000 and price_incr more than 5%
    def get_df_passing_strategy_candle_delta_gt_50000_and_price_incr_5_perc(self, df):
        df['prev_price'] = df.groupby('symbol')['price'].shift()
        df = df[(df['price']>df['prev_price']*1.05) & ((df['price']-df['prev_price'])*df['volume']>=50000)]
        return df

    # Candle delta > 50000 and price_incr more than 10%
    def get_df_passing_strategy_candle_delta_gt_50000_and_price_incr_10_perc(self, df):
        df['prev_price'] = df.groupby('symbol')['price'].shift()
        df = df[(df['price']>df['prev_price']*1.1) & ((df['price']-df['prev_price'])*df['volume']>=50000)]
        return df

    # Candle delta > 50000 and price_incr more than 5% and only ce symbols
    def get_df_passing_strategy_candle_delta_gt_50000_and_price_incr_5_perc_ce(self, df):
        df['prev_price'] = df.groupby('symbol')['price'].shift()
        df = df[(df['price']>df['prev_price']*1.05) & ((df['price']-df['prev_price'])*df['volume']>=50000) & (df['ce_pe']=='CE')]
        return df

    # Candle delta > 50000 and price_incr more than 10% and only pe symbols
    def get_df_passing_strategy_candle_delta_gt_50000_and_price_incr_10_perc_ce(self, df):
        df['prev_price'] = df.groupby('symbol')['price'].shift()
        df = df[(df['price']>df['prev_price']*1.1) & ((df['price']-df['prev_price'])*df['volume']>=50000) & (df['ce_pe']=='CE')]
        return df

    # Candle delta > 100000 and price_incr more than 5% and only ce symbols
    def get_df_passing_strategy_candle_delta_gt_100000_and_price_incr_5_perc_ce(self, df):
        df['prev_price'] = df.groupby('symbol')['price'].shift()
        df = df[(df['price']>df['prev_price']*1.05) & ((df['price']-df['prev_price'])*df['volume']>=100000) & (df['ce_pe']=='CE')]
        return df

    # Candle delta > 50000 and price_incr more than 10% and only pe symbols
    def get_df_passing_strategy_candle_delta_gt_100000_and_price_incr_10_perc_ce(self, df):
        df['prev_price'] = df.groupby('symbol')['price'].shift()
        df = df[(df['price']>df['prev_price']*1.1) & ((df['price']-df['prev_price'])*df['volume']>=100000) & (df['ce_pe']=='CE')]
        return df


    # Candle delta > 50000 and price_incr more than 5% and only pe symbols
    def get_df_passing_strategy_candle_delta_gt_50000_and_price_incr_5_perc_pe(self, df):
        df['prev_price'] = df.groupby('symbol')['price'].shift()
        df = df[(df['price']>df['prev_price']*1.05) & ((df['price']-df['prev_price'])*df['volume']>=50000) & (df['ce_pe']=='PE')]
        return df

    # Candle delta > 50000 and price_incr more than 10% and only pe symbols
    def get_df_passing_strategy_candle_delta_gt_50000_and_price_incr_10_perc_pe(self, df):
        df['prev_price'] = df.groupby('symbol')['price'].shift()
        df = df[(df['price']>df['prev_price']*1.1) & ((df['price']-df['prev_price'])*df['volume']>=50000) & (df['ce_pe']=='PE')]
        return df

    # Candle delta > 20000 and price_incr more than 5% and only pe symbols
    def get_df_passing_strategy_candle_delta_gt_20000_and_price_incr_5_perc_pe(self, df):
        df['prev_price'] = df.groupby('symbol')['price'].shift()
        df = df[(df['price']>df['prev_price']*1.05) & ((df['price']-df['prev_price'])*df['volume']>=20000) & (df['ce_pe']=='PE')]
        return df

    # Candle delta > 20000 and price_incr more than 10% and only pe symbols
    def get_df_passing_strategy_candle_delta_gt_20000_and_price_incr_10_perc_pe(self, df):
        df['prev_price'] = df.groupby('symbol')['price'].shift()
        df = df[(df['price']>df['prev_price']*1.1) & ((df['price']-df['prev_price'])*df['volume']>=20000) & (df['ce_pe']=='PE')]
        return df


def run_strategy(strategy, success_multiplier):
    d = DataAnalyzer()
    print("[[  Starting for: ", strategy, success_multiplier, "  ]]")
    months = [3]
    trading_days = [d.get_trading_days(2024, month) for month in months]
    trading_days = [item for sublist in trading_days for item in sublist]
    trading_days = [t for t in trading_days if t.day <= 26 and t.month <= 2]
    # trading_days = [t for t in trading_days if t.day==25 and t.month==1]
    df_saveable = pd.DataFrame()
    avg_success_rate = 0
    total_trades = 0
    for t in trading_days:
        df = d.get_transformed_df(day=t.day, month=t.month, year=t.year)
        df_pre_filtered = d.pre_filtering(df)

        df_filtered = getattr(d, f'get_df_passing_strategy_{strategy}')(df_pre_filtered)
        # print(df_filtered)
        df_post_strategy = d.post_strategy(df_filtered)
        # print(df_post_strategy)
        df_buys = d.clean_duplicated(df_post_strategy)
        # print(df_buys)
        df_success = d.get_success(df_buys, df, success_multiplier)
        df_saveable = pd.concat([df_saveable, df_success])
        # print(df_success)
        success_rate = d.success_rate(df_success)
        if math.isnan(success_rate):
            success_rate = 0
        avg_success_rate += success_rate
        total_trades += len(df_buys)
        # print(t, " num_trades:", len(df_buys), " success_rate:", round(success_rate, 2))

    avg_success_rate /= len(trading_days)
    print("Net Output: stratgy: ", strategy, "success_multiplier: ", success_multiplier, "total_trades: ",total_trades, "avg_success_rate: ", round(avg_success_rate, 2))
    df_saveable['strategy'] = strategy
    df_saveable['success_multiplier'] = success_multiplier

    df_saveable.to_csv(f'strategy/strategy_{strategy}_success_multiplier_{success_multiplier}.csv', index=False)
    
    # print("Saved csv")

# months = [1, 2]
# months = [1, 2]

strategy_list = [
    # '3_consecutive_incr_5_perc',
    # 'traded_val_gt_500000',
    # 'traded_val_gt_1000000',
    # 'candle_delta_gt_50000_and_price_incr_5_perc_ce',
    # 'candle_delta_gt_50000_and_price_incr_10_perc_ce',
    'candle_delta_gt_100000_and_price_incr_5_perc_ce',
    'candle_delta_gt_100000_and_price_incr_10_perc_ce',
    'candle_delta_gt_50000_and_price_incr_5_perc_pe',
    'candle_delta_gt_50000_and_price_incr_10_perc_pe',
    'candle_delta_gt_20000_and_price_incr_5_perc_pe',
    'candle_delta_gt_20000_and_price_incr_10_perc_pe',
    'candle_delta_gt_50000_and_price_incr_10_perc',
    'candle_delta_gt_50000_and_price_incr_5_perc',
    'candle_delta_gt_50000_and_price_incr_1_perc',
]

success_multiplier_list = [1.1, 1.2]
import multiprocessing

def execute_strategy(strategy_and_multiplier):
    strategy, success_multiplier = strategy_and_multiplier
    return run_strategy(strategy, success_multiplier)

if __name__ == '__main__':
    with multiprocessing.Pool() as pool:
        results = pool.map(execute_strategy, [(strategy, success_multiplier) for strategy in strategy_list for success_multiplier in success_multiplier_list])
        for result in results:
            print(result)
