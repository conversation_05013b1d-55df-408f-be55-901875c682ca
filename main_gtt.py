from broker.interfaces import BrokerI
import utils
import pdb
from quote import Quote
import yaml
import constants
from config import Config
import time
from position import Position
from constants import Literal
from trade import Trade
from kiteconnect_extras import KiteExt
import logging
import calendar
import os
from broker import Zerodha
# from broker import Upstox
from strategy import RSIBuyStrategy
from strategy import RSISellStrategy
import django
import sys
from datetime import datetime
import pytz
IST = pytz.timezone('Asia/Kolkata')
# import os
# import sys
# import django
# BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
# sys.path.append(BASE_DIR)
# os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'proj.settings')
# django.setup()
from algorithm import Algo


if __name__ == "__main__":
    config = utils.get_config()
    print(config)
    # t = TelegramUtil(config)
    broker = Zerodha(
        config.zerodha_username,
        api_key=config.zerodha_api_key,
        access_token=config.zerodha_access_token
    )
    algo = Algo(config, broker)
    while True:
        positions = [p for p in algo.broker.positions() if p['quantity'] > 0]
        sell_orders_ = [so for so in algo.broker.orders() if
                        so['status'] in ['TRIGGER PENDING']
                        and so['transaction_type'] == 'SELL']
        if not positions and sell_orders_:
            for o in sell_orders_:
                algo.broker.cancel_order(o['order_id'])
        for p in positions:
            # print(p)
            tradingsymbol = p['tradingsymbol']
            quantity = p['quantity']
            last_price = p['last_price']
            product = p['product']
            existing_gtts_ = [g for g in algo.broker.get_gtts() if g['condition']['tradingsymbol'] == tradingsymbol and g['status'] == 'active']
            existing_gtt = existing_gtts_[0] if existing_gtts_ else None
            # print(existing_gtt)
            print("orders: ", algo.broker.orders())
            sell_orders_for_symbol = [so for so in sell_orders_ if so['tradingsymbol'] == tradingsymbol]
            sell_order = sell_orders_[0] if sell_orders_for_symbol else None
            if not existing_gtt and not sell_order:
                print('placing_gtt')
                # algo.broker.place_sl_target_gtt(tradingsymbol=tradingsymbol, quantity=quantity, curr_price=last_price, sl_price=last_price-10, target_price=last_price+12, product=product)
                algo.broker.place_order(tradingsymbol=tradingsymbol, transaction_type='SELL', quantity=quantity,
                    product=product, order_type='SL', price=last_price - 12,
                    trigger_price=last_price - 10)
        time.sleep(2)



