-- Script to backfill calculated columns (ema9, ema20, vwap, price_change) in OHLC timeframe tables
-- This script processes records chronologically to ensure proper calculation dependencies

-- =====================================================
-- BACKFILL STRATEGY
-- =====================================================
/*
Strategy: For each table and symbol, process candles in chronological order (oldest first).
We'll update the price field to its existing value to trigger the BEFORE UPDATE triggers,
which will calculate all dependent fields (ema9, ema20, vwap, price_change).

This ensures:
1. EMAs are calculated in proper sequence (each EMA depends on previous EMA)
2. Price changes are calculated from correct previous candle
3. VWAP is calculated correctly based on the timeframe strategy
4. All calculations respect the chronological dependencies
*/

-- =====================================================
-- FUNCTION: Backfill calculated columns for a specific table
-- =====================================================

CREATE OR REPLACE FUNCTION backfill_calculated_columns(table_name TEXT)
RETURNS TABLE(
    processed_symbols INTEGER,
    processed_records INTEGER,
    processing_time INTERVAL
) 
LANGUAGE plpgsql
AS $function$
DECLARE
    symbol_record RECORD;
    candle_record RECORD;
    symbol_count INTEGER := 0;
    record_count INTEGER := 0;
    start_time TIMESTAMP := clock_timestamp();
    update_query TEXT;
BEGIN
    RAISE NOTICE 'Starting backfill for table: %', table_name;
    
    -- Get all unique symbols in the table that have NULL calculated columns
    FOR symbol_record IN 
        EXECUTE format('
            SELECT DISTINCT symbol 
            FROM %I 
            WHERE ema9 IS NULL OR ema20 IS NULL OR vwap IS NULL OR price_change IS NULL
            ORDER BY symbol
        ', table_name)
    LOOP
        symbol_count := symbol_count + 1;
        RAISE NOTICE 'Processing symbol % (% of total)', symbol_record.symbol, symbol_count;
        
        -- For each symbol, process candles in chronological order (oldest first)
        FOR candle_record IN
            EXECUTE format('
                SELECT time, symbol, price
                FROM %I
                WHERE symbol = $1
                AND (ema9 IS NULL OR ema20 IS NULL OR vwap IS NULL OR price_change IS NULL)
                ORDER BY time ASC
            ', table_name)
            USING symbol_record.symbol
        LOOP
            record_count := record_count + 1;

            -- Trigger recalculation by updating price to its existing value
            -- This will trigger the BEFORE UPDATE trigger which calculates all fields
            EXECUTE format('
                UPDATE %I
                SET price = $3
                WHERE symbol = $1 AND time = $2
            ', table_name)
            USING candle_record.symbol, candle_record.time, candle_record.price;
            
            -- Progress update every 100 records
            IF record_count % 100 = 0 THEN
                RAISE NOTICE 'Processed % records so far...', record_count;
            END IF;
        END LOOP;
        
        -- Progress update every 10 symbols
        IF symbol_count % 10 = 0 THEN
            RAISE NOTICE 'Completed % symbols so far...', symbol_count;
        END IF;
    END LOOP;
    
    RAISE NOTICE 'Backfill completed for %: % symbols, % records in %', 
                 table_name, symbol_count, record_count, clock_timestamp() - start_time;
    
    RETURN QUERY SELECT symbol_count, record_count, clock_timestamp() - start_time;
END;
$function$;

-- =====================================================
-- EXECUTION SECTION
-- =====================================================

-- Backfill all timeframe tables
-- Process in order from smallest to largest timeframe for logical progression

DO $backfill$
DECLARE
    result_record RECORD;
    total_symbols INTEGER := 0;
    total_records INTEGER := 0;
    total_start_time TIMESTAMP := clock_timestamp();
BEGIN
    RAISE NOTICE '========================================';
    RAISE NOTICE 'STARTING OHLC CALCULATED COLUMNS BACKFILL';
    RAISE NOTICE '========================================';
    
    -- Backfill ohlc_5min
    RAISE NOTICE '';
    RAISE NOTICE '--- Processing ohlc_5min ---';
    SELECT * INTO result_record FROM backfill_calculated_columns('ohlc_5min');
    total_symbols := total_symbols + result_record.processed_symbols;
    total_records := total_records + result_record.processed_records;
    RAISE NOTICE 'ohlc_5min completed: % symbols, % records in %', 
                 result_record.processed_symbols, result_record.processed_records, result_record.processing_time;
    
    -- Backfill ohlc_15min
    RAISE NOTICE '';
    RAISE NOTICE '--- Processing ohlc_15min ---';
    SELECT * INTO result_record FROM backfill_calculated_columns('ohlc_15min');
    total_symbols := total_symbols + result_record.processed_symbols;
    total_records := total_records + result_record.processed_records;
    RAISE NOTICE 'ohlc_15min completed: % symbols, % records in %', 
                 result_record.processed_symbols, result_record.processed_records, result_record.processing_time;
    
    -- Backfill ohlc_1hour
    RAISE NOTICE '';
    RAISE NOTICE '--- Processing ohlc_1hour ---';
    SELECT * INTO result_record FROM backfill_calculated_columns('ohlc_1hour');
    total_symbols := total_symbols + result_record.processed_symbols;
    total_records := total_records + result_record.processed_records;
    RAISE NOTICE 'ohlc_1hour completed: % symbols, % records in %', 
                 result_record.processed_symbols, result_record.processed_records, result_record.processing_time;
    
    -- Backfill ohlc_daily
    RAISE NOTICE '';
    RAISE NOTICE '--- Processing ohlc_daily ---';
    SELECT * INTO result_record FROM backfill_calculated_columns('ohlc_daily');
    total_symbols := total_symbols + result_record.processed_symbols;
    total_records := total_records + result_record.processed_records;
    RAISE NOTICE 'ohlc_daily completed: % symbols, % records in %', 
                 result_record.processed_symbols, result_record.processed_records, result_record.processing_time;
    
    RAISE NOTICE '';
    RAISE NOTICE '========================================';
    RAISE NOTICE 'BACKFILL SUMMARY';
    RAISE NOTICE '========================================';
    RAISE NOTICE 'Total symbols processed: %', total_symbols;
    RAISE NOTICE 'Total records processed: %', total_records;
    RAISE NOTICE 'Total processing time: %', clock_timestamp() - total_start_time;
    RAISE NOTICE 'Average records per second: %', 
                 CASE WHEN EXTRACT(EPOCH FROM clock_timestamp() - total_start_time) > 0 
                      THEN total_records / EXTRACT(EPOCH FROM clock_timestamp() - total_start_time)
                      ELSE 0 END;
    RAISE NOTICE '========================================';
END;
$backfill$;

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Check the status of calculated columns after backfill
DO $verify$
DECLARE
    table_name TEXT;
    null_count INTEGER;
    total_count INTEGER;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '========================================';
    RAISE NOTICE 'VERIFICATION: Checking for remaining NULL values';
    RAISE NOTICE '========================================';

    FOR table_name IN VALUES ('ohlc_5min'), ('ohlc_15min'), ('ohlc_1hour'), ('ohlc_daily')
    LOOP
        -- Check for NULL values in calculated columns
        EXECUTE format('
            SELECT COUNT(*) FROM %I
            WHERE ema9 IS NULL OR ema20 IS NULL OR vwap IS NULL OR price_change IS NULL
        ', table_name) INTO null_count;

        EXECUTE format('SELECT COUNT(*) FROM %I', table_name) INTO total_count;

        RAISE NOTICE '%: % NULL records out of % total (%.2f%% complete)',
                     table_name, null_count, total_count,
                     CASE WHEN total_count > 0 THEN ((total_count - null_count)::FLOAT / total_count * 100) ELSE 0 END;
    END LOOP;

    RAISE NOTICE '========================================';
END;
$verify$;

-- =====================================================
-- USAGE INSTRUCTIONS
-- =====================================================

/*
USAGE INSTRUCTIONS:

1. **Prerequisites**:
   - Ensure the continuous trigger functions are already installed
   - Run update_triggers_continuous.sql first if not already done

2. **Execute this script**:
   \i backfill_calculated_columns.sql

3. **What this script does**:
   - Processes each table (ohlc_5min, ohlc_15min, ohlc_1hour, ohlc_daily)
   - For each symbol, processes candles chronologically (oldest first)
   - Triggers recalculation by updating price field to its existing value
   - BEFORE UPDATE triggers automatically calculate all dependent fields

4. **Monitoring**:
   - Progress messages show symbols and records processed
   - Final summary shows total processing time and performance
   - Verification section shows remaining NULL values

5. **Performance Notes**:
   - Processing time depends on data volume
   - Expect ~100-1000 records per second depending on system
   - Large datasets may take several minutes to hours

6. **Safety**:
   - Script only processes records with NULL calculated columns
   - Uses transactions for data integrity
   - Can be safely re-run if interrupted

7. **Troubleshooting**:
   - If script fails, check that triggers are properly installed
   - Verify table schemas match expected structure
   - Check for data integrity issues (missing price/volume data)

Example output:
NOTICE:  Starting backfill for table: ohlc_15min
NOTICE:  Processing symbol AARTIIND24DEC1000CE (1 of total)
NOTICE:  Processed 100 records so far...
NOTICE:  ohlc_15min completed: 150 symbols, 25000 records in 00:02:30
*/

-- Clean up the function after use
DROP FUNCTION IF EXISTS backfill_calculated_columns(TEXT);
