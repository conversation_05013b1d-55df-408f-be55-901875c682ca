# import logging
# from html import escape
# from uuid import uuid4
# from csv import DictReader
# import difflib
# import utils
# # import config
# config = utils.get_config()
# from broker import Zerodha
# import prettytable as pt
# import psycopg2
# import pandas as pd
# import matplotlib.pyplot as plt

# broker = Zerodha(
    # config.zerodha_username,
    # api_key=config.zerodha_api_key,
    # access_token=config.zerodha_access_token
# )

# db_conn = psycopg2.connect(
#     host=config.db_host,
#     database=config.db_name,
#     user=config.db_user,
#     port=config.db_port
# )

# table_1_week = pt.PrettyTable(['Time', 'Symbol', 'Buy', 'High', 'PNL'])
# exited_positions_query_1_week = f"""
#     SELECT time, symbol, price, high_price from inline_bot_positions where high_price is not null and time>current_date - interval '7 days' order by time
# """
# with db_conn.cursor() as cur:
#     cur.execute(exited_positions_query_1_week)
#     columns = [column[0] for column in cur.description]
#     positions_1_week = []
#     for row in cur.fetchall():
#         positions_1_week.append(dict(zip(columns, row)))
#     for p in positions_1_week:
#         high_price = p.get('high_price', 0)
#         pnl = round(high_price-p['price'], 2)
#         pnl_percentage = str(round((pnl/p['price'])*100 , 2))+'%'
#         table_1_week.add_row([p['time'].strftime("%Y-%m-%d"), p['symbol'], p['price'], high_price, pnl_percentage])

#     df = pd.DataFrame(positions_1_week)
#     fig, ax = plt.subplots(figsize=(12, 10))  # Create a figure and axis

#     table = plt.table(cellText=df.values,  # Use DataFrame values for cell text
#                     colLabels=df.columns,  # Use DataFrame column names for column labels
#                     cellLoc='center',  # Align cell text at the center
#                     colColours=['#f1f1f1', '#f1f1f1', '#f1f1f1', '#f1f1f1'],  # Set column colors
#                     # colWidths=[0.1, 0.1, 0.1, 0.1, 0.1],  # Set column widths
#                     # cellColours=[['#f1f1f1', '#f1f1f1', '#f1f1f1', '#f1f1f1']]*len(df),  # Set cell colors
#                     # cellFont={'family': 'serif', 'size': 12},  # Set cell font properties
#                     bbox=[0, 0, 1, 1]
#                     )  # Set the table position and size
#     # table.set_font_properties(family='serif')  # Set the font family
#     table.auto_set_font_size(True)  # Disable automatic font size adjustment
#     # table.set_fontsize(14)  # Set the font size for all cells
#     table.scale(1, 1.5)  # Adjust the cell height and width

#     ax.axis('off')  # Disable axis
#     plt.show()  # Display the table    
#     # df_table_1_week.plot(kind="bar")




from telegram_utils import TelegramUtil
from broker import Zerodha
import time
import psycopg2
import psycopg2.pool
import logging
from multiprocessing import Process
import urllib.request
from broker_singleton import BrokerSingleton
from unusual_option_activity import UnusualOptionActivity
from filtered_instruments import FilteredInstruments
import calendar
import pandas as pd
import utils
import glob
pd.options.mode.copy_on_write = True

config = utils.get_config()
broker = BrokerSingleton()
# print(config.zerodha_username, config.zerodha_enctoken, config.zerodha_public_token)
# q = broker.get_quote('NFO:'+'ASIANPAINT24MAR3000CE')['NFO:'+'ASIANPAINT24MAR3000CE']

# print(q)


# Append all CSV files into a single dataframe in the directory strategy/*.csv
df = pd.concat([pd.read_csv(file) for file in glob.glob('strategy/*.csv')])
# import pdb; pdb.set_trace()
# print(df)

df[['date', 'timestamp']] = df['time'].str.split(expand=True)

df['profit'] = df['sell_price'] > df['price']

df = df[df['success_multiplier']==1.2]

df2 = df[(df['timestamp'] >= '09:30:00') & (df['timestamp'] < '10:00:00')]

df_grouped = df2.groupby('strategy')['profit'].mean() * 100
import pdb; pdb.set_trace()