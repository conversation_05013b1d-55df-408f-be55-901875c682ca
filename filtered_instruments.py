import pandas as pd

class FilteredInstruments:
    def __init__(self, broker) -> None:
        self.broker = broker
        self._load_raw_data()
        self._process_stocks()
        # self._process_indices()
        self._finalize_data()

    def _load_raw_data(self):
        """Load and prepare raw instruments data."""
        self.df_raw = pd.read_csv('zerodha_instruments.csv')
        self.df_raw['expiry'] = pd.to_datetime(self.df_raw['expiry'], infer_datetime_format=True)

    def _process_stocks(self):
        """Process and filter stock instruments."""
        # Filter NSE stocks
        self.nse_stocks = self.df_raw.loc[
            (self.df_raw['exchange'] == 'NSE') & (self.df_raw['segment'] == 'NSE')
        ]

        # Filter NFO stocks (excluding indices)
        excluded_indices = ['NIFTY', 'BANKNIFTY', 'FINNIFTY', 'MIDCPNIFTY']
        self.df_stocks = self.df_raw.loc[
            (self.df_raw['exchange'] == 'NFO') & ~self.df_raw['name'].isin(excluded_indices)
        ]

        # Get nearest expiry for stocks
        stocks_expiries = sorted(
            self.df_stocks.sort_values('expiry').reset_index(drop=True)['expiry'].unique()
        )[:1]
        self.df_stocks = self.df_stocks[self.df_stocks['expiry'].isin(stocks_expiries)]

        # Get FNO futures for spot price lookup
        self.df_fno_stocks = self.df_stocks.loc[
            (self.df_stocks['exchange'] == 'NFO') &
            (self.df_stocks['segment'] == 'NFO-FUT') &
            ~self.df_stocks['name'].isin(excluded_indices)
        ]
        fno_stocks = self.df_fno_stocks['name'].to_list()

        # Get spot prices and filter data
        spot_prices = self.broker.get_bulk_quote(instruments=fno_stocks, exchange='NSE')

        self.nse_stocks = self.nse_stocks.loc[
            self.nse_stocks['tradingsymbol'].isin(fno_stocks)
        ]
        self.nse_stocks['name'] = self.nse_stocks['tradingsymbol']

        # Add spot prices and apply strike filters
        self.df_stocks['spot_price'] = self.df_stocks['name'].apply(lambda x: spot_prices[x])
        self.df_stocks = self.df_stocks.loc[
            (((self.df_stocks['instrument_type'] == 'CE') &
              (self.df_stocks['strike'].between(self.df_stocks['spot_price'] * 1.01,
                                               self.df_stocks['spot_price'] * 1.11))) |
             ((self.df_stocks['instrument_type'] == 'PE') &
              (self.df_stocks['strike'].between(self.df_stocks['spot_price'] * 0.89,
                                               self.df_stocks['spot_price'] * 0.99))))
        ]
        self.df_stocks = pd.concat([self.df_stocks, self.nse_stocks])

    def _process_indices(self):
        """Process and filter index instruments."""
        # Load indices data
        self.df_indices = pd.read_csv('zerodha_instruments.csv')
        self.df_indices['expiry'] = pd.to_datetime(self.df_indices['expiry'], infer_datetime_format=True)
        self.df_indices = self.df_indices.loc[
            (self.df_indices['exchange'] == 'NFO') &
            self.df_indices['name'].isin(['NIFTY', 'BANKNIFTY', 'FINNIFTY', 'MIDCPNIFTY'])
        ]

        # Get next two expiries for each index
        nifty_expiries = sorted(
            self.df_indices[self.df_indices['name'] == 'NIFTY']
            .sort_values('expiry').reset_index(drop=True)['expiry'].unique()
        )[:2]
        banknifty_expiries = sorted(
            self.df_indices[self.df_indices['name'] == 'BANKNIFTY']
            .sort_values('expiry').reset_index(drop=True)['expiry'].unique()
        )[:2]
        finnifty_expiries = sorted(
            self.df_indices[self.df_indices['name'] == 'FINNIFTY']
            .sort_values('expiry').reset_index(drop=True)['expiry'].unique()
        )[:2]
        midcpnifty_expiries = sorted(
            self.df_indices[self.df_indices['name'] == 'MIDCPNIFTY']
            .sort_values('expiry').reset_index(drop=True)['expiry'].unique()
        )[:2]

        all_expiries = (nifty_expiries + banknifty_expiries +
                       finnifty_expiries + midcpnifty_expiries)
        self.df_indices = self.df_indices[self.df_indices['expiry'].isin(all_expiries)]

        # Get spot prices for indices
        spot_prices_raw = self.broker.get_bulk_quote(
            instruments=['NIFTY 50', 'NIFTY BANK', 'NIFTY FIN SERVICE', 'NIFTY MID SELECT'],
            exchange='NSE'
        )
        spot_prices = {
            'NIFTY': spot_prices_raw['NIFTY 50'],
            'BANKNIFTY': spot_prices_raw['NIFTY BANK'],
            'FINNIFTY': spot_prices_raw['NIFTY FIN SERVICE'],
            'MIDCPNIFTY': spot_prices_raw['NIFTY MID SELECT']
        }

        # Add spot prices and apply strike filters
        self.df_indices['spot_price'] = self.df_indices['name'].apply(lambda x: spot_prices[x])
        self.df_indices = self.df_indices.loc[
            (((self.df_indices['instrument_type'] == 'CE') &
              (self.df_indices['strike'].between(self.df_indices['spot_price'] * 1.002,
                                                self.df_indices['spot_price'] * 1.02))) |
             ((self.df_indices['instrument_type'] == 'PE') &
              (self.df_indices['strike'].between(self.df_indices['spot_price'] * 0.98,
                                                self.df_indices['spot_price'] * 0.998))))
        ]

    def _finalize_data(self):
        """Combine dataframes and add final columns."""
        # self.df = pd.concat([self.df_indices, self.df_stocks])
        self.df = self.df_stocks
        self.df['day_volume'] = 0
