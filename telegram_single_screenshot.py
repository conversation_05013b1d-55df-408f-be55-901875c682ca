from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service
import time
import os
from datetime import datetime

def take_single_telegram_screenshot(channel_url, output_dir="screenshots", user_data_dir=None, headless=True):
    """
    Take a single screenshot of the most recent messages in a Telegram channel (mobile view)

    Args:
        channel_url: URL of the Telegram channel (e.g., 'https://web.telegram.org/k/#@channelname')
        output_dir: Directory to save the screenshot
        user_data_dir: Path to Chrome user data directory to maintain login sessions
        headless: Whether to run in headless mode (True for servers, False for local testing)
    """
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)

    # Create a directory for Chrome profile if it doesn't exist
    if user_data_dir is None:
        # Default location for Chrome profile
        user_data_dir = os.path.join(os.path.expanduser("~"), "telegram_selenium_profile")

    os.makedirs(user_data_dir, exist_ok=True)

    # Setup Chrome options for mobile view
    options = webdriver.ChromeOptions()

    # Headless mode for servers
    if headless:
        options.add_argument("--headless=new")  # Use new headless mode
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--disable-gpu")
        options.add_argument("--remote-debugging-port=9222")
        print("🖥️ Headless mode enabled (for server environments)")
    else:
        print("🖥️ GUI mode enabled (for local testing)")

    # Mobile device emulation (iPhone 13 Pro)
    mobile_emulation = {
        "deviceMetrics": {
            "width": 375,
            "height": 812,
            "pixelRatio": 3.0
        },
        "userAgent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1"
    }
    options.add_experimental_option("mobileEmulation", mobile_emulation)
    options.add_argument("--window-size=375,812")
    options.add_argument(f"--user-data-dir={user_data_dir}")
    options.add_argument("--disable-blink-features=AutomationControlled")
    options.add_experimental_option("excludeSwitches", ["enable-automation"])
    options.add_experimental_option('useAutomationExtension', False)

    # Initialize the driver with webdriver-manager
    try:
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=options)
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    except Exception as e:
        print(f"Failed to initialize Chrome driver: {e}")
        print("Falling back to system ChromeDriver...")
        driver = webdriver.Chrome(options=options)

    # Set mobile viewport (if not headless)
    if not headless:
        driver.set_window_size(375, 812)
    print("📱 Mobile view enabled (375x812 - iPhone 13 Pro size)")

    try:
        # Navigate to Telegram Web
        driver.get("https://web.telegram.org/a/")
        print("Navigating to Telegram Web...")

        # Check if we need to log in
        time.sleep(3)  # Wait for page to load

        # Check for various login indicators
        login_required = any([
            "auth-form" in driver.page_source,
            "login" in driver.current_url.lower(),
            "auth" in driver.current_url.lower()
        ])

        if login_required:
            print("First-time login required. Please log in manually...")
            print("Waiting for login completion...")
            # Wait for the user to log in (look for various possible elements)
            try:
                WebDriverWait(driver, 120).until(
                    lambda d: any([
                        d.find_elements(By.CLASS_NAME, "chat-list"),
                        d.find_elements(By.CLASS_NAME, "chatlist"),
                        d.find_elements(By.CLASS_NAME, "sidebar-left"),
                        "web.telegram.org/a/#" in d.current_url
                    ])
                )
                print("Successfully logged in! This session will be saved for future use.")
            except TimeoutException:
                print("Login timeout. Please try again.")
                return
        else:
            print("Already logged in from previous session")

        # Navigate to the specific channel using the proven method
        print(f"Navigating to channel: {channel_url}")

        navigation_successful = False

        # Use the only method that works: Open in new tab and switch
        try:
            print("Opening channel in new tab...")

            # Open new tab with the channel URL
            driver.execute_script(f"window.open('{channel_url}', '_blank');")
            time.sleep(5)

            # Switch to the new tab
            if len(driver.window_handles) > 1:
                driver.switch_to.window(driver.window_handles[-1])
                time.sleep(8)

                current_url = driver.current_url
                print(f"Current URL: {current_url}")

                if channel_url.split('#')[-1] in current_url:
                    print("✓ Navigation successful")
                    navigation_successful = True
                else:
                    print("✗ Navigation failed")
                    # Switch back to original tab
                    driver.switch_to.window(driver.window_handles[0])

        except Exception as e:
            print(f"✗ Navigation failed: {e}")

        if not navigation_successful:
            print("⚠ Warning: Could not navigate to specific channel. Taking screenshot of current page.")
            print("💡 Tip: Make sure you have access to this channel and it's visible in your chat list.")

        # Debug: Print final URL and page title
        print(f"Final URL: {driver.current_url}")
        print(f"Page title: {driver.title}")

        # Wait for channel content to load with multiple possible selectors
        print("Waiting for channel content to load...")
        channel_loaded = False
        # Mobile-optimized selectors
        selectors_to_try = [
            (By.CLASS_NAME, "bubbles"),
            (By.CLASS_NAME, "bubble"),
            (By.CLASS_NAME, "messages-container"),
            (By.CLASS_NAME, "message"),
            (By.CLASS_NAME, "messages"),
            (By.CSS_SELECTOR, ".messages-container"),
            (By.CSS_SELECTOR, "[class*='bubble']"),
            (By.CSS_SELECTOR, "[class*='message']"),
            (By.CSS_SELECTOR, ".message-list"),
            (By.CSS_SELECTOR, ".chat-messages"),
            (By.CSS_SELECTOR, ".conversation"),
            (By.TAG_NAME, "main"),
            (By.CSS_SELECTOR, "[role='main']")
        ]

        for selector_type, selector_value in selectors_to_try:
            try:
                print(f"Trying selector: {selector_type} = '{selector_value}'")
                WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((selector_type, selector_value))
                )
                print(f"✓ Found element with selector: {selector_type} = '{selector_value}'")
                channel_loaded = True
                break
            except TimeoutException:
                print(f"✗ Timeout waiting for: {selector_type} = '{selector_value}'")
                continue

        if not channel_loaded:
            print("Could not find channel content with any known selector.")
            print("Attempting to take screenshot anyway...")
            # Debug: Save page source for inspection
            with open("debug_page_source.html", "w", encoding="utf-8") as f:
                f.write(driver.page_source)
            print("Page source saved to debug_page_source.html for inspection")
        else:
            print("Channel loaded successfully")

        # Scroll to the bottom to see the most recent messages
        print("Scrolling to bottom for latest messages...")
        driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        time.sleep(3)  # Wait for content to load

        # Try to scroll to the bottom of the chat area specifically (mobile-optimized)
        try:
            # Look for scrollable chat container (mobile selectors)
            chat_containers = [
                ".messages-container",
                ".bubbles-container",
                "[class*='messages']",
                "[class*='chat']",
                ".message-list",
                ".chat-messages",
                ".conversation",
                "main"
            ]

            for container_selector in chat_containers:
                try:
                    driver.execute_script(f"""
                        const container = document.querySelector('{container_selector}');
                        if (container) {{
                            container.scrollTop = container.scrollHeight;
                            console.log('Scrolled container: {container_selector}');
                        }}
                    """)
                except Exception:
                    continue

            # Additional mobile scroll attempt
            driver.execute_script("""
                // Try to scroll the main content area
                const mainContent = document.querySelector('main') || document.querySelector('[role="main"]');
                if (mainContent) {
                    mainContent.scrollTop = mainContent.scrollHeight;
                }

                // Also try scrolling the window itself
                window.scrollTo(0, document.body.scrollHeight);
            """)

        except Exception as e:
            print(f"Could not scroll chat container: {e}")

        time.sleep(2)  # Wait for scroll to complete

        # Take the screenshot
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        screenshot_path = os.path.join(output_dir, f"telegram_mobile_messages_{timestamp}.png")

        try:
            success = driver.save_screenshot(screenshot_path)
            if success:
                print(f"✓ Screenshot saved to {screenshot_path}")

                # Get file size for verification
                file_size = os.path.getsize(screenshot_path)
                print(f"Screenshot file size: {file_size} bytes")

                if file_size < 1000:  # Very small file might indicate an issue
                    print("⚠ Warning: Screenshot file is very small, might be empty or corrupted")
            else:
                print("✗ Failed to save screenshot")
        except Exception as e:
            print(f"✗ Error taking screenshot: {e}")

    except Exception as e:
        print(f"✗ Unexpected error: {e}")
        import traceback
        traceback.print_exc()

    finally:
        # Close the browser
        try:
            driver.quit()
            print("Browser closed successfully")
        except Exception as e:
            print(f"Error closing browser: {e}")



def main():
    """Main function to run the screenshot tool"""
    print("=== Telegram Channel Screenshot Tool ===")
    print()

    # Default channel URL - the one you specified
    channel_url = "https://web.telegram.org/a/#-1002075758731"

    # Auto-detect if running on a server (headless environment)
    # Check for common server environment indicators
    is_server = (
        os.environ.get('DISPLAY') is None or  # No display server
        os.environ.get('SSH_CONNECTION') is not None or  # SSH connection
        os.environ.get('CI') is not None or  # CI environment
        os.environ.get('HEADLESS') == 'true'  # Explicit headless flag
    )

    if is_server:
        print("🖥️ Server environment detected - using headless mode")
        headless = True
    else:
        print("💻 Local environment detected - using GUI mode")
        headless = False

    print(f"Taking mobile screenshot of channel: {channel_url}")
    print()
    output_dir = os.path.join(os.path.expanduser("~"), "screenshots")
    # Use default path (in user's home directory)
    try:
        take_single_telegram_screenshot(channel_url=channel_url, output_dir=output_dir, headless=headless)
        print()
        print("=== Screenshot process completed ===")
    except KeyboardInterrupt:
        print("\n=== Process interrupted by user ===")
    except Exception as e:
        print(f"\n=== Error occurred: {e} ===")

if __name__ == "__main__":
    main()
