from telethon import TelegramClient
from trade_observer import TradeObserver
import utils

class TweetHandler:
    def __init__(self, trade_observer: TradeObserver, telegram_client: TelegramClient, twitter_recos_to_telegram_channel):
        self.trade_observer = trade_observer
        self.telegram_client = telegram_client
        self.twitter_recos_to_telegram_channel = twitter_recos_to_telegram_channel

    async def tweet_handler_dancing_pappu(self, text):
        script = 'NIFTY'
        tradingsymbol = 'NIFTY2262315500CE'
        quantity = 250
        price = 100.0
        self.trade_observer.update(script=script, tradingsymbol=tradingsymbol, price=price)


    async def tweet_handler_AgileInvestmen1(self, text):
        print(text)
        text = text.replace('.', '')
        script = None
        text = text.split(' ')
        if text[0] == 'Bought' and ('CE' in text or 'PE' in text):
            script = text[1].upper()
            strike_price = text[2]
            ce_pe = text[3]
            tradingsymbol = utils.next_expiry_prefix(script) + strike_price + ce_pe
            price = int(text[4].split('.')[0])

        self.trade_observer.update(script, tradingsymbol=tradingsymbol, price=price)


    async def tweet_handler_ArjunDangayach(self, text):
        print(text)
        text = text.replace(',', '')
        text = text.replace('.', '')
        text = text.split(' ')
        if text[0] == 'Buy' and (text[1].endswith('CE') or text[1].endswith('PE')):
            strike_price = text[1][0:-2]
            ce_pe = text[1][-2:]
            script = utils.get_nifty_banknifty_prefix(int(strike_price))
            tradingsymbol = utils.next_expiry_prefix(script) + strike_price + ce_pe
            for i in range(2,len(text)):
                if text[i] == '@':
                    price = int(text[i+1])
                    break
                elif text[i].startswith('@'):
                    price = int(text[i][1:])
                    break
            self.trade_observer.update(script, tradingsymbol=tradingsymbol, price=price)


    async def tweet_handler(self, author, tweet_id, text):
        tweet_url = 'https://twitter.com/'+author+'/status/'+tweet_id
        trigger_alert = any(t.isdigit() for t in text.split(' '))
        print(trigger_alert)
        if author == 'dancing_pappu':
            author = 'AgileInvestmen1'
        if trigger_alert:
            await self.telegram_client.send_message(self.twitter_recos_to_telegram_channel, tweet_url)
            tweet_handler_author = None
            try:
                tweet_handler_author = getattr(self, 'tweet_handler_'+author)
            except AttributeError:
                pass
            if tweet_handler_author:
                await tweet_handler_author(text)
