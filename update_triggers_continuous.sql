-- Script to update OHLC triggers for continuous calculations across day changes
-- This script removes daily session constraints from EMA and price change calculations

-- =====================================================
-- STEP 1: Drop existing triggers from timeframe tables
-- =====================================================

-- Drop triggers from ohlc_15min
DROP TRIGGER IF EXISTS trg_ohlc_15min_before_insert_emas ON ohlc_15min;
DROP TRIGGER IF EXISTS trg_ohlc_15min_before_insert_price_change ON ohlc_15min;

-- Drop triggers from ohlc_1hour  
DROP TRIGGER IF EXISTS trg_ohlc_1hour_before_insert_emas ON ohlc_1hour;
DROP TRIGGER IF EXISTS trg_ohlc_1hour_before_insert_price_change ON ohlc_1hour;

-- Drop triggers from ohlc_daily
DROP TRIGGER IF EXISTS trg_ohlc_daily_before_insert_emas ON ohlc_daily;
DROP TRIGGER IF EXISTS trg_ohlc_daily_before_insert_price_change ON ohlc_daily;

-- =====================================================
-- STEP 2: Create new continuous calculation functions
-- =====================================================

-- Function for continuous EMA calculation (no daily reset)
CREATE OR REPLACE FUNCTION public.calculate_continuous_emas()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
DECLARE
    prev_candle_time TIMESTAMP WITH TIME ZONE;
    prev_ema9 DOUBLE PRECISION;
    prev_ema20 DOUBLE PRECISION;
    alpha9 DOUBLE PRECISION := 2.0 / (9 + 1);   -- Multiplier for 9-period EMA
    alpha20 DOUBLE PRECISION := 2.0 / (20 + 1); -- Multiplier for 20-period EMA
    table_name TEXT;
BEGIN
    -- Get the table name from TG_TABLE_NAME
    table_name := TG_TABLE_NAME;
    
    -- Find the timestamp of the immediately preceding candle for the same symbol
    -- No daily session constraints - look for any previous candle
    EXECUTE format('
        SELECT MAX(t.time)
        FROM %I t
        WHERE t.symbol = $1
        AND t.time < $2
    ', table_name)
    INTO prev_candle_time
    USING NEW.symbol, NEW.time;

    -- If prev_candle_time is NULL, this is the first candle for this symbol
    IF prev_candle_time IS NULL THEN
        NEW.ema9 := NEW.price;  -- Seed EMA with the current price
        NEW.ema20 := NEW.price; -- Seed EMA with the current price
    ELSE
        -- Fetch the EMAs from that specific previous candle
        EXECUTE format('
            SELECT t.ema9, t.ema20
            FROM %I t
            WHERE t.symbol = $1
            AND t.time = $2
        ', table_name)
        INTO prev_ema9, prev_ema20
        USING NEW.symbol, prev_candle_time;

        -- Handle case where previous EMAs might be NULL
        IF prev_ema9 IS NULL THEN
            NEW.ema9 := NEW.price;
        ELSE
            NEW.ema9 := (NEW.price * alpha9) + (prev_ema9 * (1.0 - alpha9));
        END IF;

        IF prev_ema20 IS NULL THEN
            NEW.ema20 := NEW.price;
        ELSE
            NEW.ema20 := (NEW.price * alpha20) + (prev_ema20 * (1.0 - alpha20));
        END IF;
    END IF;

    RETURN NEW; -- Return the modified row to be inserted
END;
$function$;

-- Function for continuous price change calculation (no daily reset)
CREATE OR REPLACE FUNCTION public.calculate_continuous_price_change()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
DECLARE
    prev_candle_time_found TIMESTAMP WITH TIME ZONE;
    prev_candle_price DOUBLE PRECISION;
    table_name TEXT;
BEGIN
    -- Get the table name from TG_TABLE_NAME
    table_name := TG_TABLE_NAME;
    
    -- Find the price of the immediately preceding candle for the same symbol
    -- No daily session constraints - look for any previous candle
    EXECUTE format('
        SELECT time, price
        FROM %I t
        WHERE t.symbol = $1
        AND t.time < $2
        ORDER BY t.time DESC
        LIMIT 1
    ', table_name)
    INTO prev_candle_time_found, prev_candle_price
    USING NEW.symbol, NEW.time;

    IF prev_candle_time_found IS NULL OR prev_candle_price IS NULL OR prev_candle_price = 0 THEN
        -- This is the first candle for this symbol, or previous price was 0
        NEW.price_change := 0.0;
    ELSE
        NEW.price_change := ((NEW.price - prev_candle_price) / prev_candle_price) * 100.0;
    END IF;

    RETURN NEW; -- Return the modified row to be inserted
END;
$function$;

-- Function for continuous VWAP calculation (moving VWAP of last 5 candles)
CREATE OR REPLACE FUNCTION public.calculate_continuous_vwap()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
DECLARE
    sum_price_times_volume NUMERIC; -- Using NUMERIC for precision in sum
    sum_volume BIGINT;              -- Assuming volume can be large
    table_name TEXT;
BEGIN
    -- Get the table name from TG_TABLE_NAME
    table_name := TG_TABLE_NAME;

    -- Get the last 5 candles (including current one) for the same symbol
    -- Calculate VWAP from these candles
    EXECUTE format('
        SELECT
            COALESCE(SUM(t.price * t.volume), 0),
            COALESCE(SUM(t.volume), 0)
        FROM (
            SELECT price, volume
            FROM %I t
            WHERE t.symbol = $1
            AND t.time <= $2
            ORDER BY t.time DESC
            LIMIT 5
        ) t
    ', table_name)
    INTO sum_price_times_volume, sum_volume
    USING NEW.symbol, NEW.time;

    -- Add the current (NEW) candle's contribution if not already included
    -- Since we're using <= in the query above, the current candle should be included
    -- But we need to subtract it first and then add it to ensure consistency
    sum_price_times_volume := sum_price_times_volume - (NEW.price * NEW.volume);
    sum_volume := sum_volume - NEW.volume;

    -- Now add the current candle
    sum_price_times_volume := sum_price_times_volume + (NEW.price * NEW.volume);
    sum_volume := sum_volume + NEW.volume;

    -- Calculate VWAP and assign to the new row
    IF sum_volume > 0 THEN
        NEW.vwap := sum_price_times_volume / sum_volume;
    ELSE
        -- If sum_volume is 0, VWAP is simply the price of this candle
        NEW.vwap := NEW.price;
    END IF;

    RETURN NEW; -- Return the modified row to be inserted
END;
$function$;

-- Function for daily VWAP calculation (generic version)
CREATE OR REPLACE FUNCTION public.calculate_daily_vwap()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
DECLARE
    daily_start_time TIMESTAMP WITH TIME ZONE;
    sum_price_times_volume NUMERIC; -- Using NUMERIC for precision in sum
    sum_volume BIGINT;              -- Assuming volume can be large
    table_name TEXT;
BEGIN
    -- Get the table name from TG_TABLE_NAME
    table_name := TG_TABLE_NAME;

    -- Determine the start of the trading day for the NEW row's timestamp
    -- This assumes your NEW.time is in a timezone that makes sense for 'Asia/Kolkata'
    -- or that it's in UTC and you want to define day start relative to 'Asia/Kolkata' 09:15
    daily_start_time := date_trunc('day', NEW.time AT TIME ZONE 'Asia/Kolkata') AT TIME ZONE 'Asia/Kolkata' + INTERVAL '9 hours 15 minutes';

    -- Aggregate Price*Volume and Volume for the current symbol from the start of the day
    -- up to (but not including) the current new row.
    EXECUTE format('
        SELECT
            COALESCE(SUM(t.price * t.volume), 0),
            COALESCE(SUM(t.volume), 0)
        FROM %I t
        WHERE t.symbol = $1
        AND t.time >= $2
        AND t.time < $3
    ', table_name)
    INTO sum_price_times_volume, sum_volume
    USING NEW.symbol, daily_start_time, NEW.time;

    -- Add the current (NEW) candle's contribution
    sum_price_times_volume := sum_price_times_volume + (NEW.price * NEW.volume);
    sum_volume := sum_volume + NEW.volume;

    -- Calculate VWAP and assign to the new row
    IF sum_volume > 0 THEN
        NEW.vwap := sum_price_times_volume / sum_volume;
    ELSE
        -- If sum_volume is 0 (e.g., it's the very first candle of the day for this symbol,
        -- or the first candle has 0 volume), VWAP is simply the price of this candle.
        NEW.vwap := NEW.price;
    END IF;

    RETURN NEW; -- Return the modified row to be inserted
END;
$function$;

-- Function for daily EMA calculation (generic version)
CREATE OR REPLACE FUNCTION public.calculate_daily_emas()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
DECLARE
    daily_start_time TIMESTAMP WITH TIME ZONE;
    prev_candle_time TIMESTAMP WITH TIME ZONE;
    prev_ema9 DOUBLE PRECISION;
    prev_ema20 DOUBLE PRECISION;
    alpha9 DOUBLE PRECISION := 2.0 / (9 + 1);   -- Multiplier for 9-period EMA
    alpha20 DOUBLE PRECISION := 2.0 / (20 + 1); -- Multiplier for 20-period EMA
    table_name TEXT;
BEGIN
    -- Get the table name from TG_TABLE_NAME
    table_name := TG_TABLE_NAME;

    -- Determine the start of the trading day for the NEW row's timestamp
    daily_start_time := date_trunc('day', NEW.time AT TIME ZONE 'Asia/Kolkata') AT TIME ZONE 'Asia/Kolkata' + INTERVAL '9 hours 15 minutes';

    -- Find the timestamp of the immediately preceding candle for the same symbol within today's session
    EXECUTE format('
        SELECT MAX(t.time)
        FROM %I t
        WHERE t.symbol = $1
        AND t.time < $2
        AND t.time >= $3
    ', table_name)
    INTO prev_candle_time
    USING NEW.symbol, NEW.time, daily_start_time;

    -- If prev_candle_time is NULL, it means this is the first candle of the day for this symbol,
    -- or the first candle after daily_start_time if data starts mid-day.
    IF prev_candle_time IS NULL THEN
        NEW.ema9 := NEW.price;  -- Seed EMA with the current price
        NEW.ema20 := NEW.price; -- Seed EMA with the current price
    ELSE
        -- Fetch the EMAs from that specific previous candle
        EXECUTE format('
            SELECT t.ema9, t.ema20
            FROM %I t
            WHERE t.symbol = $1
            AND t.time = $2
        ', table_name)
        INTO prev_ema9, prev_ema20
        USING NEW.symbol, prev_candle_time;

        -- Handle case where previous EMAs might be NULL
        IF prev_ema9 IS NULL THEN
            NEW.ema9 := NEW.price;
        ELSE
            NEW.ema9 := (NEW.price * alpha9) + (prev_ema9 * (1.0 - alpha9));
        END IF;

        IF prev_ema20 IS NULL THEN
            NEW.ema20 := NEW.price;
        ELSE
            NEW.ema20 := (NEW.price * alpha20) + (prev_ema20 * (1.0 - alpha20));
        END IF;
    END IF;

    RETURN NEW; -- Return the modified row to be inserted
END;
$function$;

-- Function for daily price change calculation (generic version)
CREATE OR REPLACE FUNCTION public.calculate_daily_price_change()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
DECLARE
    daily_start_time TIMESTAMP WITH TIME ZONE;
    prev_candle_time_found TIMESTAMP WITH TIME ZONE;
    prev_candle_price DOUBLE PRECISION;
    table_name TEXT;
BEGIN
    -- Get the table name from TG_TABLE_NAME
    table_name := TG_TABLE_NAME;

    -- Determine the start of the trading day for the NEW row's timestamp
    -- Assuming candle timestamps are like 09:16:00 for the 09:15:00-09:15:59 interval
    daily_start_time := (date_trunc('day', NEW.time AT TIME ZONE 'Asia/Kolkata') + INTERVAL '9 hours 16 minutes') AT TIME ZONE 'Asia/Kolkata';

    -- Find the price of the immediately preceding candle for the same symbol within today's session
    EXECUTE format('
        SELECT time, price
        FROM %I t
        WHERE t.symbol = $1
        AND t.time < $2
        AND t.time >= $3
        ORDER BY t.time DESC
        LIMIT 1
    ', table_name)
    INTO prev_candle_time_found, prev_candle_price
    USING NEW.symbol, NEW.time, daily_start_time;

    IF prev_candle_time_found IS NULL OR prev_candle_price IS NULL OR prev_candle_price = 0 THEN
        -- This is the first candle of the day for this symbol,
        -- or previous price was 0 (to avoid division by zero).
        -- Set percentage change to 0 for the first candle.
        NEW.price_change := 0.0;
    ELSE
        NEW.price_change := ((NEW.price - prev_candle_price) / prev_candle_price) * 100.0;
    END IF;

    RETURN NEW; -- Return the modified row to be inserted
END;
$function$;


-- =====================================================
-- STEP 4: Create new triggers for daily calculations
-- =====================================================

-- Create triggers for ohlc_1min
CREATE TRIGGER trg_ohlc_1min_before_insert_emas
    BEFORE INSERT 
    OR UPDATE 
    ON ohlc_1min
    FOR EACH ROW
    EXECUTE FUNCTION calculate_daily_emas_1min();

CREATE TRIGGER trg_ohlc_1min_before_insert_price_change
    BEFORE INSERT 
    OR UPDATE 
    ON ohlc_1min
    FOR EACH ROW
    EXECUTE FUNCTION calculate_daily_price_change_1min();

CREATE TRIGGER trg_ohlc_1min_before_insert_vwap
    BEFORE INSERT
    OR UPDATE
    ON ohlc_1min
    FOR EACH ROW
    EXECUTE FUNCTION calculate_daily_vwap_1min();

-- Create triggers for ohlc_5min
CREATE TRIGGER trg_ohlc_5min_before_insert_emas
    BEFORE INSERT 
    OR UPDATE 
    ON ohlc_5min
    FOR EACH ROW
    EXECUTE FUNCTION calculate_daily_emas();

CREATE TRIGGER trg_ohlc_5min_before_insert_price_change
    BEFORE INSERT 
    OR UPDATE 
    ON ohlc_5min
    FOR EACH ROW
    EXECUTE FUNCTION calculate_daily_price_change();

CREATE TRIGGER trg_ohlc_5min_before_insert_vwap
    BEFORE INSERT
    OR UPDATE
    ON ohlc_5min
    FOR EACH ROW
    EXECUTE FUNCTION calculate_daily_vwap();


-- =====================================================
-- STEP 4: Create new triggers for continuous calculations
-- =====================================================

-- Create triggers for ohlc_15min
CREATE TRIGGER trg_ohlc_15min_before_insert_emas
    BEFORE INSERT 
    OR UPDATE 
    ON ohlc_15min
    FOR EACH ROW
    EXECUTE FUNCTION calculate_continuous_emas();

CREATE TRIGGER trg_ohlc_15min_before_insert_price_change
    BEFORE INSERT 
    OR UPDATE 
    ON ohlc_15min
    FOR EACH ROW
    EXECUTE FUNCTION calculate_continuous_price_change();

-- Use the generic VWAP trigger (daily calculation is appropriate for VWAP)
CREATE TRIGGER trg_ohlc_15min_before_insert_vwap
    BEFORE INSERT
    OR UPDATE
    ON ohlc_15min
    FOR EACH ROW
    EXECUTE FUNCTION calculate_continuous_vwap();

-- Create triggers for ohlc_1hour
CREATE TRIGGER trg_ohlc_1hour_before_insert_emas
    BEFORE INSERT OR UPDATE ON ohlc_1hour
    FOR EACH ROW
    EXECUTE FUNCTION calculate_continuous_emas();

CREATE TRIGGER trg_ohlc_1hour_before_insert_price_change
    BEFORE INSERT OR UPDATE ON ohlc_1hour
    FOR EACH ROW
    EXECUTE FUNCTION calculate_continuous_price_change();

-- Use the continuous VWAP trigger (moving VWAP of last 5 candles)
CREATE TRIGGER trg_ohlc_1hour_before_insert_vwap
    BEFORE INSERT OR UPDATE ON ohlc_1hour
    FOR EACH ROW
    EXECUTE FUNCTION calculate_continuous_vwap();

-- Create triggers for ohlc_daily
CREATE TRIGGER trg_ohlc_daily_before_insert_emas
    BEFORE INSERT OR UPDATE ON ohlc_daily
    FOR EACH ROW
    EXECUTE FUNCTION calculate_continuous_emas();

CREATE TRIGGER trg_ohlc_daily_before_insert_price_change
    BEFORE INSERT OR UPDATE ON ohlc_daily
    FOR EACH ROW
    EXECUTE FUNCTION calculate_continuous_price_change();

-- Use the continuous VWAP trigger (moving VWAP of last 5 candles)
CREATE TRIGGER trg_ohlc_daily_before_insert_vwap
    BEFORE INSERT OR UPDATE ON ohlc_daily
    FOR EACH ROW
    EXECUTE FUNCTION calculate_continuous_vwap();

-- =====================================================
-- STEP 4: Verification queries
-- =====================================================

-- Check that triggers are created correctly
SELECT
    n.nspname as schemaname,
    c.relname as tablename,
    t.tgname as triggername,
    pg_get_triggerdef(t.oid) as triggerdef
FROM pg_trigger t
JOIN pg_class c ON t.tgrelid = c.oid
JOIN pg_namespace n ON c.relnamespace = n.oid
WHERE c.relname IN ('ohlc_15min', 'ohlc_1hour', 'ohlc_daily')
AND NOT t.tgisinternal
ORDER BY c.relname, t.tgname;

-- =====================================================
-- USAGE INSTRUCTIONS
-- =====================================================

/*
To execute this script:

1. Connect to your PostgreSQL database
2. Run this script: \i update_triggers_continuous.sql

Key Changes Made:
- Created continuous versions: calculate_continuous_emas(), calculate_continuous_price_change(), and calculate_continuous_vwap()
  * Removed daily trading session time constraints from EMA and price change calculations
  * EMAs now continue across day boundaries using the last available EMA values
  * Price changes now calculate from the immediately previous candle regardless of day
  * VWAP now calculates moving VWAP of last 5 candles (used for ohlc_1hour and ohlc_daily)
- Created generic daily versions: calculate_daily_emas_generic(), calculate_daily_price_change_generic(), calculate_daily_vwap_generic()
  * All functions now use dynamic table names (TG_TABLE_NAME) for flexibility
  * Preserve daily session logic but work with any OHLC table
- All trigger functions now use dynamic table names for maximum flexibility

Impact:
- EMA9 and EMA20 will now be truly continuous across trading days
- Price changes will show change from previous candle even across day boundaries
- VWAP for ohlc_1hour and ohlc_daily now uses moving 5-candle window for smoother trends
- ohlc_15min still uses daily VWAP (more appropriate for intraday analysis)
- This provides better technical analysis continuity for multi-day trends

Tables Affected:
- ohlc_15min
- ohlc_1hour
- ohlc_daily

Note: ohlc_1min table is not modified as it uses the original daily functions
which are still appropriate for minute-level data within trading sessions.

Function Options Available:
1. Continuous Functions (used in this script):
   - calculate_continuous_emas(): EMAs continue across day boundaries
   - calculate_continuous_price_change(): Price changes across day boundaries
   - calculate_continuous_vwap(): Moving VWAP of last 5 candles (used for ohlc_1hour and ohlc_daily)

2. Generic Daily Functions (also created in this script):
   - calculate_daily_emas_generic(): Daily EMAs with dynamic table names
   - calculate_daily_price_change_generic(): Daily price changes with dynamic table names
   - calculate_daily_vwap_generic(): Daily VWAP with dynamic table names

3. Original Functions (not modified):
   - calculate_daily_emas(): Original daily EMA function for ohlc_1min
   - calculate_daily_price_change(): Original daily price change function for ohlc_1min
   - calculate_daily_vwap(): Original daily VWAP function for ohlc_1min

This script uses:
- Continuous functions for EMAs and price changes (all tables)
- Continuous VWAP for ohlc_1hour and ohlc_daily (moving 5-candle window)
- Daily VWAP for ohlc_15min (intraday session-based)
You can easily switch between continuous and daily versions by changing the trigger function calls.
*/
