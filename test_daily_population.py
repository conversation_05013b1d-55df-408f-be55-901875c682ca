#!/usr/bin/env python3
"""
Test script for populate_daily_historical.py
Tests the daily OHLC population with a few stocks before running the full script.
"""

import psycopg2
import utils
import logging
from datetime import datetime, timedelta
from populate_daily_historical import OHLCHistoricalPopulator

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s %(levelname)s: %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S"
)

def test_broker_connection():
    """Test broker connection and API access."""
    logging.info("--- Testing Broker Connection ---")

    try:
        populator = DailyOHLCPopulator()

        # Test basic broker functionality
        logging.info("Broker initialized successfully")
        logging.info(f"Found {len(populator.nse_stocks)} NSE F&O stocks")

        # Show sample stocks
        sample_stocks = populator.nse_stocks.head(5)
        logging.info("Sample stocks:")
        for _, stock in sample_stocks.iterrows():
            logging.info(f"  {stock['tradingsymbol']} (token: {stock['instrument_token']})")

        populator.close_connection()
        return True

    except Exception as e:
        logging.error(f"Broker connection test failed: {e}")
        return False

def test_historical_api():
    """Test Zerodha historical API with one stock."""
    logging.info("--- Testing Historical API ---")

    try:
        populator = DailyOHLCPopulator()

        # Get first stock for testing
        test_stock = populator.nse_stocks.iloc[0]
        symbol = test_stock['tradingsymbol']

        logging.info(f"Testing historical API with {symbol}")

        # Fetch data for last 7 days
        daily_data = populator.fetch_daily_data_for_stock(test_stock)

        if daily_data:
            logging.info(f"✅ Successfully fetched {len(daily_data)} daily records")
            logging.info("Sample data:")
            for record in daily_data[:3]:
                logging.info(f"  {record}")
        else:
            logging.warning("⚠️  No data received")

        populator.close_connection()
        return len(daily_data) > 0

    except Exception as e:
        logging.error(f"Historical API test failed: {e}")
        import traceback
        logging.error(f"Traceback: {traceback.format_exc()}")
        return False

def test_database_insertion():
    """Test database insertion with sample data."""
    logging.info("--- Testing Database Insertion ---")

    try:
        populator = DailyOHLCPopulator()

        # Create sample data
        sample_data = [
            {
                'time': datetime.now().replace(hour=15, minute=30, second=0, microsecond=0),
                'symbol': 'TEST_SYMBOL',
                'price': 100.50,
                'volume': 1000,
                'oi': 500
            }
        ]

        logging.info("Inserting sample data...")
        populator.insert_daily_data(sample_data)

        # Verify insertion
        with populator.db_conn.cursor() as cur:
            cur.execute("SELECT * FROM ohlc_daily WHERE symbol = 'TEST_SYMBOL'")
            result = cur.fetchone()

            if result:
                logging.info(f"✅ Sample data inserted successfully: {result}")

                # Clean up test data
                cur.execute("DELETE FROM ohlc_daily WHERE symbol = 'TEST_SYMBOL'")
                populator.db_conn.commit()
                logging.info("Test data cleaned up")
            else:
                logging.error("❌ Sample data not found in database")
                return False

        populator.close_connection()
        return True

    except Exception as e:
        logging.error(f"Database insertion test failed: {e}")
        return False

def test_limited_population():
    """Test population with 2-3 stocks."""
    logging.info("--- Testing Limited Population ---")

    try:
        populator = DailyOHLCPopulator()

        # Limit to first 3 stocks
        original_stocks = populator.nse_stocks
        populator.nse_stocks = populator.nse_stocks.head(3)

        logging.info(f"Testing with {len(populator.nse_stocks)} stocks:")
        for _, stock in populator.nse_stocks.iterrows():
            logging.info(f"  {stock['tradingsymbol']}")

        # Populate with last 7 days data
        successful, failed, total_records = populator.populate_all_stocks(days_back=7, skip_existing=False)

        logging.info(f"Population results: {successful} successful, {failed} failed, {total_records} total records")

        # Verify data
        populator.verify_data()

        populator.close_connection()
        return successful > 0

    except Exception as e:
        logging.error(f"Limited population test failed: {e}")
        return False

def check_existing_data():
    """Check existing data in ohlc_daily table."""
    logging.info("--- Checking Existing Data ---")

    config = utils.get_config()

    try:
        db_conn = psycopg2.connect(
            host=config.db_host,
            database=config.db_name,
            user=config.db_user,
            port=config.db_port
        )

        with db_conn.cursor() as cur:
            # Check total records
            cur.execute("SELECT COUNT(*) FROM ohlc_daily")
            total_count = cur.fetchone()[0]

            if total_count > 0:
                # Check unique symbols
                cur.execute("SELECT COUNT(DISTINCT symbol) FROM ohlc_daily")
                symbol_count = cur.fetchone()[0]

                # Check date range
                cur.execute("SELECT MIN(time), MAX(time) FROM ohlc_daily")
                min_date, max_date = cur.fetchone()

                logging.info(f"Existing data: {total_count} records, {symbol_count} symbols")
                logging.info(f"Date range: {min_date} to {max_date}")

                # Show sample data
                cur.execute("""
                    SELECT symbol, time, price, volume
                    FROM ohlc_daily
                    ORDER BY time DESC
                    LIMIT 5
                """)
                sample_data = cur.fetchall()
                logging.info("Recent data:")
                for row in sample_data:
                    logging.info(f"  {row}")
            else:
                logging.info("No existing data in ohlc_daily table")

        db_conn.close()
        return True

    except Exception as e:
        logging.error(f"Error checking existing data: {e}")
        return False

def main():
    """Run all tests."""
    logging.info("Starting populate_daily_historical.py tests...")

    tests = [
        ("Existing Data Check", check_existing_data),
        ("Broker Connection", test_broker_connection),
        ("Historical API", test_historical_api),
        ("Database Insertion", test_database_insertion),
        ("Limited Population", test_limited_population),
    ]

    results = {}
    for test_name, test_func in tests:
        logging.info(f"\n{'='*50}")
        logging.info(f"Running: {test_name}")
        logging.info('='*50)

        try:
            results[test_name] = test_func()
        except Exception as e:
            logging.error(f"Test {test_name} failed with exception: {e}")
            results[test_name] = False

    # Summary
    logging.info(f"\n{'='*50}")
    logging.info("TEST RESULTS SUMMARY")
    logging.info('='*50)

    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        logging.info(f"{test_name}: {status}")

    all_passed = all(results.values())
    if all_passed:
        logging.info("\n🎉 All tests passed! Ready to run populate_daily_historical.py")
        logging.info("\nUsage examples:")
        logging.info("  python populate_daily_historical.py                    # Last 30 days")
        logging.info("  python populate_daily_historical.py test               # Test with 5 stocks")
        logging.info("  python populate_daily_historical.py custom 2024-01-01 2024-01-31  # Custom range")
    else:
        logging.error("\n⚠️  Some tests failed. Please fix issues before running the main script.")

if __name__ == "__main__":
    main()
