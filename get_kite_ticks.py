import sys
import json
import logging
import time
from threading import Thread
from twisted.python import log
from kiteconnect_extras import KiteExt
from twisted.internet import reactor
from autobahn.twisted.websocket import listenWS
from localwsbroadcastserver import (
    DateTimeEncoder,
    BroadcastServerFactory,
    BroadcastServerProtocol,
)
import asyncio
import pandas as pd
from datetime import datetime, timedelta
# logging.basicConfig(level=logging.DEBUG)
import utils
config=utils.get_config()
from broker import Zerodha

class LocalWsBroadCast:
    def __init__(self):
        log.startLogging(sys.stdout)
        opts = {"installSignalHandlers": False}
        self.ServerFactory = BroadcastServerFactory
        ws_url = "ws://127.0.0.1:9000"
        self.factory = self.ServerFactory(ws_url)
        self.factory.protocol = BroadcastServerProtocol
        listenWS(self.factory)
        self.broker = Zerodha(
            config.zerodha_username,
            api_key=config.zerodha_api_key,
            access_token=config.zerodha_access_token
        )
        self.df = pd.read_csv('/Users/<USER>/Downloads/zerodha_instruments.csv')
        self.df['expiry'] = pd.to_datetime(self.df['expiry'], infer_datetime_format=True)
        self.df = self.df.loc[(self.df['exchange']=='NFO') & ~self.df['name'].isin(['NIFTY', 'BANKNIFTY', 'FINNIFTY', 'MIDCPNIFTY'])]
        current_month = datetime.now().month
        fno_stocks = self.df.loc[(self.df['exchange']=='NFO') & (self.df['expiry'].dt.month == current_month) & (self.df['segment']=='NFO-FUT') & ~self.df['name'].isin(['NIFTY', 'BANKNIFTY', 'FINNIFTY', 'MIDCPNIFTY'])]['name'].to_list()
        spot_prices = self.broker.get_bulk_quote(instruments=fno_stocks, exchange='NSE')
        self.df['spot_price'] = self.df['name'].apply(lambda x: spot_prices[x])

        self.df = self.df.loc[
            (( (self.df['instrument_type']=='CE') & (self.df['strike'].between(self.df['spot_price']*1.05, self.df['spot_price']*1.11))) | ((self.df['instrument_type']=='PE') & (self.df['strike'].between(self.df['spot_price']*0.89, self.df['spot_price']*0.95)))) 
            & (self.df['expiry'].dt.month == current_month)
        ]

    def on_ticks(self, ws, ticks):
        # print(ticks)
        for i in range(5000):
            self.factory.broadcast(json.dumps(ticks, sort_keys=False, cls=DateTimeEncoder))
            time.sleep(1)
    def on_connect(self, ws, response):
        ws.subscribe(ws.instrument_token_list)
        ws.set_mode(ws.MODE_QUOTE, ws.instrument_token_list)

    def on_close(self, ws, code, reason):
        ws.stop()

    def on_error(self, ws, code, reason):
        print(code, reason)

    async def run(self, instrument_token_list=None):
        kws = self.broker.ticker()
        kws.instrument_token_list = instrument_token_list
        kws.on_ticks = self.on_ticks
        kws.on_connect = self.on_connect
        kws.on_close = self.on_close
        kws.on_error = self.on_error
        kws.connect(threaded=False)


async def main():
    lwb = LocalWsBroadCast()
    instruments = lwb.df['instrument_token'].to_list()
    partitions = []
    num_partitions = len(instruments)//500
    for i in range(num_partitions):
        partitions.append(instruments[i*500:i*500+500])
    partitions.append(instruments[num_partitions*500:])
    tasks = []
    for p in partitions:
        tasks.append(asyncio.create_task(lwb.run(instrument_token_list=p)))
    await asyncio.wait(tasks)

asyncio.run(main())
