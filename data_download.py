import psycopg2
import utils
config = utils.get_config()
from broker import Zerodha
import pandas as pd
from broker_singleton import Broker<PERSON>ingleton
from filtered_instruments import FilteredInstruments
broker = BrokerSingleton()
import calendar
import datetime
import time

class DataDownloader:
    def __init__(self) -> None:
        self.config = utils.get_config()
        self.broker = BrokerSingleton()

        self.db_conn = psycopg2.connect(
            host=config.db_host,
            database=config.db_name,
            user=config.db_user,
            port=config.db_port
        )

        instruments_query = f"""
            SELECT symbol, instrument_token from instruments
        """

        self.insert_query_oct = f"""
            INSERT INTO ohlc_1min_nov_2023
            (time, symbol, price, volume, oi)
            VALUES (%s, %s, %s, %s, %s)
        """


        self.instruments_list = {}
        with self.db_conn.cursor() as cur:
            cur.execute(instruments_query)
            rows = cur.fetchall()
            self.instruments_list = {r[0]: r[1] for r in rows}

    def save_quote_to_database(self, quotes):
        with self.db_conn.cursor() as cur:
            cur.executemany(self.insert_query_oct, [(q['time'], q['symbol'], q['price'], q['volume'],  q['oi']) for q in quotes])
            self.db_conn.commit()


    def get_trading_days(self, year, month, date_starting_from=None):
        # Get all the dates in a given month and year
        dates = []
        num_days = calendar.monthrange(year, month)[1]
        for day in range(1, num_days + 1):
            date = datetime.date(year, month, day)
            dates.append(date)
        holidays = ['2023-01-26', '2023-03-07', '2023-03-30', '2023-04-04', '2023-04-07', '2023-04-14', '2023-05-01', '2023-06-29', '2023-08-15', '2023-09-19', '2023-10-02', '2023-10-24', '2023-11-14', '2023-11-27', '2023-12-25']
        trading_days = [d for d in dates if d.weekday() < 5 and str(d) not in holidays]
        if date_starting_from:
            trading_days = [t for t in trading_days if t.day>=date_starting_from]
        return trading_days

    def save_open_price_for_fno_stocks(self, trading_days):
        for day in trading_days:
            for symbol, instrument_token in self.instruments_list.items():
                opening_price = broker.kite.historical_data(instrument_token=instrument_token, from_date=str(day)+' 09:15:00', to_date=str(day)+' 09:15:00', interval='minute')[0]['close']
                time.sleep(0.4)
                insert_stock_open_price_query = f"""
                    INSERT INTO stock_open_price
                    (time, symbol, price)
                    VALUES (%s, %s, %s)
                """
                with self.db_conn.cursor() as cur:
                    cur.execute(insert_stock_open_price_query, (str(day)+" 09:16:00+05:30", symbol, opening_price))
                    self.db_conn.commit()

    def save_open_price_for_index_fno_stocks(self, trading_days):
        instruments_list = {'NIFTY': 256265, 'FINNIFTY': 257801, 'BANKNIFTY': 260105, 'MIDCPNIFTY': 288009}
        for day in trading_days:
            for symbol, instrument_token in instruments_list.items():
                opening_price = broker.kite.historical_data(instrument_token=instrument_token, from_date=str(day)+' 09:15:00', to_date=str(day)+' 09:15:00', interval='minute')[0]['close']
                time.sleep(0.4)
                insert_stock_open_price_query = f"""
                    INSERT INTO stock_open_price
                    (time, symbol, price)
                    VALUES (%s, %s, %s)
                """
                with self.db_conn.cursor() as cur:
                    cur.execute(insert_stock_open_price_query, (str(day)+" 09:16:00+05:30", symbol, opening_price))
                    self.db_conn.commit()


    def get_open_price_for_fno_stocks(self, day):
        select_stock_open_price_query = f"""
            SELECT symbol, price from stock_open_price where time::date = %s
        """
        stock_prices = dict()
        with self.db_conn.cursor() as cur:
                    cur.execute(select_stock_open_price_query, (day,))
                    rows = cur.fetchall()
                    stock_prices = {r[0]: r[1] for r in rows}
        return stock_prices


def main(year, month):
    d= DataDownloader()
    month_name = calendar.month_abbr[month].upper()
    trading_days = d.get_trading_days(2023, 11)[0:4]
    # d.save_open_price_for_fno_stocks(trading_days=trading_days)
    for day in trading_days:
        stock_open_prices = d.get_open_price_for_fno_stocks(day)
        df_file_name = '/Users/<USER>/Downloads/sample/GFDLNFO_BACKADJUSTED_'+day.strftime('%d%m%Y')+'.csv'
        df = pd.read_csv(df_file_name)
        df['time'] = pd.to_datetime(df['Date'] + ' ' + df['Time']+'+05:30', format='%d/%m/%Y %H:%M:%S%z')
        df['time'] = df['time'] + pd.Timedelta(seconds=1)
        df.rename(columns={'Ticker': 'symbol', 'Close': 'price', 'Volume': 'volume', 'Open Interest': 'oi'}, inplace=True)
        df['symbol'] = df['symbol'].str[:-4]
        df = df[df['symbol'].str.endswith(('CE', 'PE'))]
        df = df[df['symbol'].str.contains(month_name)] # Filter useless symbols
        df = df[['time', 'symbol', 'price', 'volume', 'oi']]
        df['spot_symbol'] = df['symbol'].str.extract(r'^([A-Za-z&-]+)')
        df['strike_price'] = df['symbol'].str.extract(r'NOV\d{2}(\d+)').astype(float)
        df['ce_pe'] = df['symbol'].str[-2:]
        df['spot_price'] = df['spot_symbol'].apply(lambda x: stock_open_prices[x])
        for spot_symbol, price in stock_open_prices.items():
            result = df[(df['spot_symbol'] == spot_symbol) & ((df['ce_pe']=='CE') & (df['strike_price'].between(price*1.01, price*1.11)) | (df['ce_pe']=='PE') & (df['strike_price'].between(price*0.89, price*0.99)))]
            result = result[['time', 'symbol', 'price', 'volume', 'oi']]
            d.save_quote_to_database(result.to_dict(orient='records'))
            # print(day, spot_symbol, ": saved")
        print("day_finished: ", day)

main(2023, 11)
