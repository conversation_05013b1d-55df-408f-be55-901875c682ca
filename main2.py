from selenium import webdriver
from selenium.webdriver.common.by import By
# from selenium.webdriver.support.ui import Web<PERSON><PERSON><PERSON>ait
# from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.common.exceptions import NoSuchElementException, ElementNotInteractableException, InvalidCookieDomainException
from selenium.webdriver.common.keys import Keys
import time
import browser_cookie3
import os

# cookies = browser_cookie3.chrome(domain_name='kite.zerodha.com')
# print(cookies)
# enctoken = [c.value for c in cookies if c.name=='enctoken'][0]
# print(enctoken)
# for c in cookies:
#     cookie = {'domain': c.domain, 'name': c.name, 'value': c.value, 'secure': c.secure and True or False}
#     print(cookie)
    # browser.add_cookie(cookie)
# print("Exiting")
# print(browser.get_cookies()[0]['value'])
# exit()


os.system("killall -9 'Google Chrome'")


# username_str = 'DR6733'
# password_str = 'tr0ubleM@ker'
# pin_str = '100291'

options = webdriver.ChromeOptions()
options.add_argument('user-data-dir=/Users/<USER>/Library/Application Support/Google/Chrome/')
options.add_argument('--profile-directory=Default') #e.g. Profile 3
options.add_experimental_option("detach", True)
options.add_argument('disable-infobars')
browser = webdriver.Chrome(options=options)
# s = Service(ChromeDriverManager().install())
# browser = webdriver.Chrome(executable_path='./chromedriver'
#                            , options=options
#                            )
import time
print(browser.capabilities)
# time.sleep(10)
browser.get('https://kite.zerodha.com/')
# browser.get('https://kite.zerodha.com/')
# cookies = browser_cookie3.chrome(domain_name='kite.zerodha.com')
# cookies = {
#     'public_token': 'WAHt78rZWNXeQxJcbqreR7G6nngWO0UT',
#     'enctoken': 'E4zZincWuN74eE2CyYUchXz4H90nbHUxNRtqY61nAe1WAgmT9NjxecryhZLxcmpGTdH0Vp2EQm490jFW19kQq7aVOVVBVQCGlhmRSMXSSMq4kxzgI3k0UA==',
#     'user_id': 'DR6733',
#     'kf_session': 'iDLEvfDXkVPVGt3QM7Y5BNRex6UjT0aU'
# }
cookies = browser.get_cookies()
print(cookies)
for k,v in cookies.items():
    cookie = {'name': k, 'value': v}
    browser.add_cookie(cookie)
browser.get('https://kite.zerodha.com/')

# try:
#     username = browser.find_element(by=By.ID, value='userid')
# except NoSuchElementException:
#     pass
# else:
#     try:
#         username.send_keys(username_str)
#     except ElementNotInteractableException:
#         pass
# try:
#     password = browser.find_element(by=By.ID, value='password')
# except NoSuchElementException:
#     pass
# else:
#     password.send_keys(password_str)
#     password.send_keys(Keys.RETURN)

# time.sleep(1)
# try:
#     pin = browser.find_element(by=By.ID, value='pin')
#     pin.send_keys(pin_str)
#     pin.send_keys(Keys.RETURN)
# except NoSuchElementException:
#     pass
time.sleep(1)
