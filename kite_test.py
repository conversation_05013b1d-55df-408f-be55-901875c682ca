from kiteconnect import KiteConnect, KiteTicker
import pyotp
import requests
import yaml
import os
from datetime import datetime
from urllib.parse import urlparse, parse_qs
import utils
config = utils.get_config()

username = config.zerodha_username
password = config.zerodha_password
api_key = config.zerodha_api_key
totp_secret = config.zerodha_secret
api_secret = config.zerodha_api_secret
kite = KiteConnect(api_key=api_key)
session = requests.Session()
session_id = parse_qs(urlparse(session.get(kite.login_url()).url).query)['sess_id'][0]

request_id = session.post("https://kite.zerodha.com/api/login", {"user_id": username, "password": password}).json()["data"]["request_id"]

r = session.post("https://kite.zerodha.com/api/twofa", {"user_id": username, "request_id": request_id, "twofa_value": pyotp.TOTP(totp_secret).now(), "skip_session": True})

r = session.get("https://kite.zerodha.com/connect/finish?api_key=k73ue5uj00cpn3vc&sess_id="+session_id+"&skip_session=true", allow_redirects=False)

request_token = parse_qs(urlparse(r.headers['location']).query)['request_token'][0]
kite.set_access_token(kite.generate_session(request_token=request_token, api_secret=config.zerodha_api_secret)['access_token'])
