from telethon import TelegramClient
import tweepy
from tweepy.asynchronous.streaming import AsyncStreamingClient
import traceback
import os
from typing import List, Optional

from tweet_handler import Tweet<PERSON><PERSON>ler
import signal
import sys
signal.signal(signal.SIGINT, lambda _, __: sys.exit(0))

# I am working on building a bot that automatically posts my private organization's newsletter link, blog posts on multiple social media sites including twitter. This will help us avoid manual effort needed and help us achieve automation of social media marketing.
# My usecase is to build a bot that automatically posts my private organization's newsletter link, blog posts on multiple social media sites including twitter. This will help us avoid manual effort needed in social media marketing and improve the digital presence of the organization.
# My App will specifically use Tweet functionality to instantly post blog, newsletters for my organization on twitter.

import asyncio
import json

class Trade:
    def __init__(self, tradingsymbol, quantity, price):
        self.tradingsymbol = tradingsymbol
        self.quantity = quantity
        self.price = price


class TwitterPoster:
    """
    A class for posting tweets with optional media attachments.
    Supports text-only tweets and tweets with images/videos.
    """

    def __init__(self, config):
        """
        Initialize TwitterPoster with API credentials from config.

        Args:
            config: Configuration object containing Twitter API credentials
        """
        self.config = config

        # Initialize tweepy Client with API v2 and v1.1 access
        self.client = tweepy.Client(
            bearer_token=config.twitter_bearer_token,
            consumer_key=config.twitter_api_key,
            consumer_secret=config.twitter_api_secret,
            access_token=config.twitter_access_token,
            access_token_secret=config.twitter_access_token_secret,
            wait_on_rate_limit=True
        )

        # Initialize API v1.1 for media upload (required for media uploads)
        auth = tweepy.OAuth1UserHandler(
            config.twitter_api_key,
            config.twitter_api_secret,
            config.twitter_access_token,
            config.twitter_access_token_secret
        )
        self.api_v1 = tweepy.API(auth, wait_on_rate_limit=True)

    def upload_media(self, media_path: str) -> str:
        """
        Upload media file to Twitter and return media ID.

        Args:
            media_path: Path to the media file (image or video)

        Returns:
            str: Media ID from Twitter

        Raises:
            FileNotFoundError: If media file doesn't exist
            Exception: If upload fails
        """
        if not os.path.exists(media_path):
            raise FileNotFoundError(f"Media file not found: {media_path}")

        try:
            # Upload media using API v1.1
            media = self.api_v1.media_upload(media_path)
            return media.media_id_string
        except Exception as e:
            raise Exception(f"Failed to upload media {media_path}: {str(e)}")

    def post_tweet(self, text: str, media_paths: Optional[List[str]] = None) -> dict:
        """
        Post a tweet with optional media attachments.

        Args:
            text: Tweet text content
            media_paths: Optional list of paths to media files

        Returns:
            dict: Response from Twitter API

        Raises:
            Exception: If tweet posting fails
        """
        try:
            media_ids = []

            # Upload media files if provided
            if media_paths:
                for media_path in media_paths:
                    media_id = self.upload_media(media_path)
                    media_ids.append(media_id)

            # Post tweet with or without media
            if media_ids:
                response = self.client.create_tweet(text=text, media_ids=media_ids)
            else:
                response = self.client.create_tweet(text=text)

            return response.data

        except Exception as e:
            raise Exception(f"Failed to post tweet: {str(e)}")

    def post_text_only(self, text: str) -> dict:
        """
        Post a text-only tweet.

        Args:
            text: Tweet text content

        Returns:
            dict: Response from Twitter API
        """
        return self.post_tweet(text)

    def post_with_single_media(self, text: str, media_path: str) -> dict:
        """
        Post a tweet with a single media attachment.

        Args:
            text: Tweet text content
            media_path: Path to the media file

        Returns:
            dict: Response from Twitter API
        """
        return self.post_tweet(text, [media_path])

    def post_with_multiple_media(self, text: str, media_paths: List[str]) -> dict:
        """
        Post a tweet with multiple media attachments (up to 4 images or 1 video).

        Args:
            text: Tweet text content
            media_paths: List of paths to media files

        Returns:
            dict: Response from Twitter API

        Raises:
            ValueError: If more than 4 media files provided
        """
        if len(media_paths) > 4:
            raise ValueError("Twitter allows maximum 4 media files per tweet")

        return self.post_tweet(text, media_paths)


class AsyncTwitterPoster:
    """
    Async version of TwitterPoster for use in async environments.
    Provides the same functionality but with async/await support.
    """

    def __init__(self, config):
        """
        Initialize AsyncTwitterPoster with API credentials from config.

        Args:
            config: Configuration object containing Twitter API credentials
        """
        self.poster = TwitterPoster(config)

    async def upload_media(self, media_path: str) -> str:
        """
        Async wrapper for media upload.

        Args:
            media_path: Path to the media file

        Returns:
            str: Media ID from Twitter
        """
        return self.poster.upload_media(media_path)

    async def post_tweet(self, text: str, media_paths: Optional[List[str]] = None) -> dict:
        """
        Async wrapper for posting tweets.

        Args:
            text: Tweet text content
            media_paths: Optional list of paths to media files

        Returns:
            dict: Response from Twitter API
        """
        return self.poster.post_tweet(text, media_paths)

    async def post_text_only(self, text: str) -> dict:
        """
        Async wrapper for posting text-only tweets.

        Args:
            text: Tweet text content

        Returns:
            dict: Response from Twitter API
        """
        return self.poster.post_text_only(text)

    async def post_with_single_media(self, text: str, media_path: str) -> dict:
        """
        Async wrapper for posting tweets with single media.

        Args:
            text: Tweet text content
            media_path: Path to the media file

        Returns:
            dict: Response from Twitter API
        """
        return self.poster.post_with_single_media(text, media_path)

    async def post_with_multiple_media(self, text: str, media_paths: List[str]) -> dict:
        """
        Async wrapper for posting tweets with multiple media.

        Args:
            text: Tweet text content
            media_paths: List of paths to media files

        Returns:
            dict: Response from Twitter API
        """
        return self.poster.post_with_multiple_media(text, media_paths)


class TwitterStreamingClient(AsyncStreamingClient):
    def __init__(self, config, telegram_client: TelegramClient, tweet_handler: TweetHandler):
        bearer_token = config.twitter_bearer_token
        super().__init__(bearer_token)
        self.config = config
        self.telegram_client = telegram_client
        # self.algo = algo
        self.loop = asyncio.get_event_loop()
        self.tweet_handler = tweet_handler

    # def run(self):
        # return self.loop.run_until_complete(self.__async__run())
        # asyncio.run(self.__async__run())
    # def run(self):
    #     self.filter(expansions=['author_id'])

    async def run(self):
        rules_ = await self.get_rules()
        rules = rules_.data
        if rules:
            await self.delete_rules(ids=[r.id for r in rules])

        await self.add_rules(tweepy.StreamRule(
            'from:smittal2015 OR from:dancing_pappu OR from:ArjunDangayach OR from:powerofchart OR from:Lochanjain14 OR from:Stocks_Vibes OR from:youngindianwant OR from:equitysecure22 OR from:siddharth1008p1 OR mailmepurn OR RajniGhodasara OR Factomatters OR tradeiideas'
        ))
        await self.filter(expansions=['author_id'], user_fields=['username'])

    async def on_data(self, raw_data):
        # print(getframeinfo(currentframe()).lineno, tweet.text)
        raw_data = json.loads(raw_data.decode('utf-8'))
        print(raw_data)
        tweet_id = raw_data['data']['id']
        text = raw_data['data']['text']
        author = raw_data['includes']['users'][0]['username']
        try:
            await self.tweet_handler.tweet_handler(author, tweet_id, text)
        except Exception as e:
            traceback.print_exc()


# Example usage functions
def create_twitter_poster(config):
    """
    Create a TwitterPoster instance with the given config.

    Args:
        config: Configuration object with Twitter API credentials

    Returns:
        TwitterPoster: Initialized TwitterPoster instance
    """
    return TwitterPoster(config)


def create_async_twitter_poster(config):
    """
    Create an AsyncTwitterPoster instance with the given config.

    Args:
        config: Configuration object with Twitter API credentials

    Returns:
        AsyncTwitterPoster: Initialized AsyncTwitterPoster instance
    """
    return AsyncTwitterPoster(config)


# Example usage:
# if __name__ == "__main__":
#     import utils
#     config = utils.get_config()
#
#     # For synchronous usage
#     poster = create_twitter_poster(config)
#     response = poster.post_text_only("Hello Twitter!")
#     print(f"Tweet posted: {response}")
#
#     # For posting with media
#     response = poster.post_with_single_media("Check out this image!", "/path/to/image.jpg")
#     print(f"Tweet with media posted: {response}")
#
#     # For async usage
#     async def async_example():
#         async_poster = create_async_twitter_poster(config)
#         response = await async_poster.post_text_only("Hello from async!")
#         print(f"Async tweet posted: {response}")
#
#     # asyncio.run(async_example())
