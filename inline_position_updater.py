import logging
from html import escape
from uuid import uuid4
from csv import DictReader
import difflib
import utils
# import config
config = utils.get_config()
from broker import Zerodha
import prettytable as pt
import psycopg2
import urllib.request
import pandas as pd

broker = Zerodha(
    config.zerodha_username,
    api_key=config.zerodha_api_key,
    access_token=config.zerodha_access_token
)

db_conn = psycopg2.connect(
    host=config.db_host,
    database=config.db_name,
    user=config.db_user,
    port=config.db_port
)
try:
    urllib.request.urlretrieve('https://api.kite.trade/instruments', 'zerodha_instruments.csv')
except:
    pass
df_fno_symbols = pd.read_csv('zerodha_instruments.csv')
df_fno_symbols = df_fno_symbols.loc[(df_fno_symbols['exchange']=='NFO')]

# Enable logging
logging.basicConfig(
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s", level=logging.INFO
)
logger = logging.getLogger(__name__)
config = utils.get_config()


from datetime import datetime

def update_open_positions():
    position_query = f"""
        SELECT * from inline_bot_positions where status = 'OPEN' 
    """
    with db_conn.cursor() as cur:
        cur.execute(position_query)
        columns = [column[0] for column in cur.description]
        positions = []
        for row in cur.fetchall():
            positions.append(dict(zip(columns, row)))
    
    all_open_positions = [p for p in positions if p['symbol'] in df_fno_symbols['tradingsymbol'].to_list()]
    # print(positions)

    if not all_open_positions:
        return
    quotes = broker.get_quote(['NFO:'+f['symbol'] for f in all_open_positions])
    high_prices = {symbol[4:]: data['ohlc']['high'] if data['ohlc']['high'] != 0 else data['last_price'] for symbol, data in quotes.items()}
    high_prices = [(high_prices[symbol], symbol) for symbol in high_prices]
    with db_conn.cursor() as cur:
        for price, symbol in high_prices:
            cur.execute("SELECT high_price FROM inline_bot_positions WHERE symbol = %s", (symbol,))
            result = cur.fetchone()
            if result is None or result[0] is None or result[0] < price:
                cur.execute("UPDATE inline_bot_positions SET high_price = %s WHERE symbol = %s", (price, symbol))

    # Close expired_symbols
    expired_symbols = [p['symbol'] for p in positions if p['symbol'] not in df_fno_symbols['tradingsymbol'].to_list()]
    print("expired_symbols", expired_symbols)
    if expired_symbols:
        with db_conn.cursor() as cur:
            cur.execute("UPDATE inline_bot_positions SET status = 'CLOSED' WHERE symbol IN %s", (tuple(expired_symbols),))

    db_conn.commit()

update_open_positions()
