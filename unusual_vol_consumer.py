import pika
import json
from broker import Zerodha
import utils
import logging
import psycopg2
from filtered_instruments import FilteredInstruments
from telegram_utils import TelegramUtil
from datetime import datetime
import time
import asyncio
import traceback
from broker_singleton import BrokerSingleton

logging.basicConfig(level=logging.INFO,
                    format="%(asctime)s %(levelname)s: %(message)s",
                    datefmt="%Y-%m-%d %H:%M:%S")

class UnusualVolumeConsumer:
    def __init__(self):
        self.config = utils.get_config()
        self.broker = BrokerSingleton()
        self.filtered_instruments = FilteredInstruments(self.broker).df_raw
        self.db_conn = psycopg2.connect(
            host=self.config.db_host,
            database=self.config.db_name,
            user=self.config.db_user,
            port=self.config.db_port
        )
        config = utils.get_config()
        self.telegram_bot = TelegramUtil(config=config, client=False, bot=True)
        self.telegram_chat_id = -1002075758731
        self.buy_alert_query = f"""
            INSERT INTO daily_option_trades (time, symbol, buy_price) VALUES (%s, %s, %s)
        """


    def sell(self, tradingsymbol, price, qty):
        script_tick_size = self.filtered_instruments.loc[self.filtered_instruments['tradingsymbol'] == tradingsymbol, 'tick_size'].values[0]
        price = int(price*0.9/script_tick_size)*script_tick_size
        try:
            self.broker.place_order(tradingsymbol=tradingsymbol, transaction_type='SELL', quantity=qty, product='MIS',
            order_type='LIMIT', price=price, exchange='NFO')
        except:
            print(traceback.format_exc())

    def is_market_indicative(self, tradingsymbol, spot_symbol):
        qsymbol = self.broker.get_quote('NSE:'+spot_symbol)['NSE:'+spot_symbol]
        qnifty = self.broker.get_quote('NSE:'+'NIFTY 50')['NSE:'+'NIFTY 50']

        if tradingsymbol.endswith('CE'):
            return qsymbol['last_price'] > qsymbol['ohlc']['open'] and qnifty['last_price'] > qnifty['ohlc']['open']
        elif tradingsymbol.endswith('PE'):
            return qsymbol['last_price'] < qsymbol['ohlc']['open'] and qnifty['last_price'] < qnifty['ohlc']['open']
        else:
            return True

    def buy(self, tradingsymbol, spot_symbol, price, target=None, sl=None):
        # import pdb; pdb.set_trace()
        script_lot_size = self.filtered_instruments.loc[self.filtered_instruments['tradingsymbol'] == tradingsymbol, 'lot_size'].values[0]
        script_tick_size = self.filtered_instruments.loc[self.filtered_instruments['tradingsymbol'] == tradingsymbol, 'tick_size'].values[0]
        price = int(price/script_tick_size)*script_tick_size
        print("tradingsymbol: ", tradingsymbol, "price: ", price)
        # live_balance = self.broker.get_margin()['available']['live_balance']
        live_balance = 1000000
        capital_per_trade = min(self.config.capital, live_balance)
        print("live_balance: ", live_balance)
        script_freeze_limit = 1000000000
        # import pdb; pdb.set_trace()
        qty = min(int((capital_per_trade/price)/script_lot_size)*script_lot_size, script_freeze_limit)
        if qty == 0:
            print("Not enough balance")
            return
        print(datetime.now(), " ", "placing_order_for: "+tradingsymbol+' @'+str(price)+ ' for size '+str(qty))

        product = 'MIS' if tradingsymbol.endswith('CE') or tradingsymbol.endswith('PE') else 'CNC'
        exchange = 'NFO' if tradingsymbol.endswith('CE') or tradingsymbol.endswith('PE') else 'NSE'

        current_time = datetime.now().time()
        # Add extra filters even posting alerts

        if (current_time > datetime.strptime("14:30", "%H:%M").time()
            or current_time < datetime.strptime("09:20", "%H:%M").time()
            # or not self.is_market_indicative(tradingsymbol, spot_symbol)
            ):
            return

        # Send msg instead of actually buying
        loop = asyncio.get_event_loop()

        msg = f"Alert: {tradingsymbol} @ {round(price, 2)}"
        loop.run_until_complete(self.telegram_bot.send_message(chatid=self.telegram_chat_id, msg=msg))


        try:
            self.broker.place_order(tradingsymbol=tradingsymbol, transaction_type='BUY', quantity=qty, product=product,
            order_type='LIMIT', price=price, exchange=exchange)
        except:
            print(traceback.format_exc())
        # if sl and target:
        #     print('placing_gtt')
        #     self.broker.place_sl_target_gtt(tradingsymbol=tradingsymbol, quantity=qty, curr_price=price, sl_price=sl, target_price=target)


    def can_trade_symbol(self, spot_symbol, symbols, positions, orders):

        current_ce_count = len([symbol for symbol in symbols if symbol['symbol'].endswith('CE')])
        current_pe_count = len([symbol for symbol in symbols if symbol['symbol'].endswith('PE')])


        with self.db_conn.cursor() as cur:
            query = f"""
                SELECT 
                    (SELECT COUNT(*) FROM unusual_option_activity WHERE spot_symbol = '{spot_symbol}' and strike_symbol LIKE '%CE' and alerted_at > CURRENT_DATE) AS ce_count,
                    (SELECT COUNT(*) FROM unusual_option_activity WHERE spot_symbol = '{spot_symbol}' and strike_symbol LIKE '%PE' and alerted_at > CURRENT_DATE) AS pe_count,
                    (SELECT spot_price FROM unusual_option_activity WHERE spot_symbol = '{spot_symbol}' order by alerted_at desc limit 1) AS latest_spot_price,
                    (SELECT spot_price FROM unusual_option_activity WHERE spot_symbol = '{spot_symbol}' and alerted_at > CURRENT_DATE order by alerted_at asc limit 1) AS initial_spot_price
            """
            cur.execute(query)
            total_ce_count, total_pe_count, latest_spot_price, initial_spot_price = cur.fetchone()
            print("spot_symbol: ", spot_symbol, "symbols: ", [(s['symbol'], s['price'], s['vwap'], s['ema9'], s['ema20']) for s in symbols], "total_ce_count: ", total_ce_count, "total_pe_count: ", total_pe_count, "current_ce_count:", current_ce_count, "current_pe_count: ", current_pe_count, "latest_spot_price: ", latest_spot_price, "initial_spot_price: ", initial_spot_price)

        # # Don't trade if both ce and pe are present
        # if total_ce_count and total_pe_count:
        #     return False
        # # Don't trade if atleast 3 ce/pe have not been received.
        # if (total_ce_count == 0 and total_pe_count < 3) or (total_pe_count == 0 and total_ce_count < 3):
        #     return False
        # # Don't trade if both ce/pe are present or if ce/pe count was already more than 0 before current alert
        # elif (total_ce_count and total_ce_count - current_ce_count > 0) or (total_pe_count and total_pe_count - current_pe_count > 0):
        #     return False

        if not ((current_ce_count >= 3 and current_pe_count == 0 and total_pe_count==0 and total_ce_count-current_ce_count<=2) 
            or (current_pe_count >= 3 and current_ce_count == 0 and total_ce_count == 0 and total_pe_count-current_pe_count<=2)
            or (total_ce_count >= 5 and total_pe_count == 0 and latest_spot_price>initial_spot_price and ((latest_spot_price - initial_spot_price) / initial_spot_price) <= 0.005)
            or (total_pe_count >= 5 and total_ce_count == 0 and initial_spot_price>latest_spot_price and ((initial_spot_price - latest_spot_price) / initial_spot_price) <= 0.005)
        ):
            return False

        position_symbols = [p['tradingsymbol'] for p in positions]
        order_symbols = [p['tradingsymbol'] for p in orders]
        for p in position_symbols:
            if p.startswith(spot_symbol):
                return False
        for o in order_symbols:
            if o.startswith(spot_symbol):
                return False

        # Only process non-empty lists
        valid_symbols = [v for v in symbols if v]  # Filter out empty lists
        if not valid_symbols:  # If no valid symbols, return False
            return False
            
        picked_symbol = valid_symbols[-1] if valid_symbols[0]['symbol'].endswith('PE') else valid_symbols[0]
        
        print("Indicator checks: ", picked_symbol['symbol'], picked_symbol['price'], picked_symbol['vwap'], picked_symbol['ema9'], picked_symbol['ema20'])
        if picked_symbol['price']<picked_symbol['vwap'] or picked_symbol['ema9']<picked_symbol['ema20']:
            return False
        # quote = self.broker.get_quote('NFO:'+symbols[0])['NFO:'+symbols[0]]
        # low_price = quote['ohlc']['low']
        # last_price = quote['last_price']

        # if last_price >= low_price*1.5:
        #     print(symbols[0], " is too highly priced")
        #     return False

        # if price<1.0 or price>100.0:
        #     return False
        return True

    def get_position_symbol_against_spot(self, spot_symbol, positions):
        return next(((p['tradingsymbol'], p['quantity'], p['last_price']) for p in positions if p['tradingsymbol'].startswith(spot_symbol) and p['quantity']!=0), (None, None, None))

    def process_events(self):
        # Connect to RabbitMQ
        connection = pika.BlockingConnection(pika.ConnectionParameters('localhost', heartbeat=10))
        channel = connection.channel()

        # Declare the same queue as in the producer
        channel.queue_declare(queue='quotes')

        # Define a callback function that will be called when a message is received
        def callback(ch, method, properties, body):
            quotes = json.loads(body)
            print("\n\n", datetime.now(), "received_trades: ", [q['symbol'] for q in quotes])

            spot_symbol_dict = {}
            for q in quotes:
                spot_symbol = q["spot_symbol"]
                symbol = q["symbol"]
                price = q['price_to']
                vwap = q['vwap']
                ema9 = q['ema9']
                ema20 = q['ema20']
                if spot_symbol in spot_symbol_dict:
                    spot_symbol_dict[spot_symbol].append({"symbol": symbol, "price": price, "spot_symbol": spot_symbol, "vwap": vwap, "ema9": ema9, "ema20": ema20})
                else:
                    spot_symbol_dict[spot_symbol] = [{"symbol": symbol, "price": price, "spot_symbol": spot_symbol, "vwap": vwap, "ema9": ema9, "ema20": ema20}]

            positions = self.broker.positions()
            orders = self.broker.orders()

            tradeable_spots = {}
            for spot_symbol, symbols in spot_symbol_dict.items():
                position_symbol_against_spot, position_symbol_against_spot_qty, position_symbol_against_spot_last_price = self.get_position_symbol_against_spot(spot_symbol, positions)

                if position_symbol_against_spot and any(
                    (position_symbol_against_spot.endswith('CE') and s['symbol'].endswith('PE')) or
                    (position_symbol_against_spot.endswith('PE') and s['symbol'].endswith('CE'))
                    for s in spot_symbol_dict[spot_symbol]
                ):
                    # Found a conflicting signal. We have a postion but the new signal is opposite. Cancel the existing order
                    script_tick_size = self.filtered_instruments.loc[self.filtered_instruments['tradingsymbol'] == position_symbol_against_spot, 'tick_size'].values[0]
                    print('Found a conflicting signal: ', position_symbol_against_spot)
                    print('Cancel all pending orders and sell asap: ', position_symbol_against_spot)
                    open_sl_order_id = next((o['order_id'] for o in orders if o['transaction_type'] == 'SELL' and o['status'] == 'OPEN' and o['order_type']=='SL' and o['tradingsymbol'] == position_symbol_against_spot), None)
                    if open_sl_order_id:
                        self.broker.cancel_order(order_id=open_sl_order_id)
                        time.sleep(1)
                    self.sell(tradingsymbol=position_symbol_against_spot, price=int(position_symbol_against_spot_last_price * 0.9 / script_tick_size) * script_tick_size, qty=position_symbol_against_spot_qty)

                if self.can_trade_symbol(spot_symbol, symbols, positions, orders):
                    tradeable_spots[spot_symbol] = symbols


            # Pick the smallest strike CE/PE from price among incoming symbols 
            tradeable_quotes = [v[-1] if v[0]['symbol'].endswith('PE') else v[0] for v in tradeable_spots.values()]

            try:
                with self.db_conn.cursor() as cur:
                    data = [(datetime.now().replace(second=0, microsecond=0), row['symbol'], row['price']) for row in tradeable_quotes]
                    cur.executemany(self.buy_alert_query, data)
                    self.db_conn.commit()
            except Exception as e:
                print(traceback.format_exc())

            # Actual buying logic
            for q in tradeable_quotes:
                self.buy(tradingsymbol=q['symbol'], spot_symbol=q['spot_symbol'], price=q['price'])

                # time.sleep(2)
        # Start consuming messages
        channel.basic_consume(queue='quotes', on_message_callback=callback, auto_ack=True)
        channel.start_consuming()

if __name__ == "__main__":
    # time.sleep(30)
    print(__file__)
    uvc = UnusualVolumeConsumer()
    fi = FilteredInstruments(uvc.broker)
    uvc.process_events()

