import constants
from condition import Condition
from constants import Literal


class Trade:
    def __init__(self, id, order_dict):
        self._order_dict = order_dict
        self.symbol = order_dict['symbol']
        self.condition = Condition(order_dict['condition'])
        self.quantity = order_dict['quantity'] * order_dict.get('lot_size', 1)
        self.exchange = Literal.NFO if (self.symbol.endswith(Literal.PE) or self.symbol.endswith(
            Literal.CE) or self.symbol.endswith(Literal.FUT)) else Literal.NSE
        self.transaction_type = order_dict['transaction_type'] if 'transaction_type' in order_dict else Literal.BUY
        self.executed = False
        self.sl = order_dict['sl']

    def __init__(self, symbol, quantity, exchange, transaction_type, sl):
        self.symbol = symbol
        self.quantity = quantity
        self.exchange = exchange
        self.transaction_type = transaction_type
        self.sl=sl

    def __repr__(self) -> str:
        return str('Trade<symbol: {}, quantity: {}, transaction: {}>'.format(self.symbol, self.quantity, self.transaction_type))

    def get_valid_tick(self, ticks):
        return ticks[0]

    # If SL value is more than 1.0, consider it absolute SL, else consider it SL percentage
    def get_absolute_sl_trigger_price(self, cmp):
        if self.transaction_type == Literal.SELL:
            self.sl = -1*self.sl
        return (cmp-self.sl) if abs(self.sl) > 1.0 else (1-self.sl)*cmp

    def condition_match(self, open_positions, tick):
        if self.executed == True:
            return False
        if self.symbol in [p.symbol for p in open_positions]:
            return False
        if self.condition.operator == '>':
            return self.tick.price > self.condition.price
        if self.condition.operator == '<':
            return self.tick.price < self.condition.price
        return False

    def set_executed(self):
        self.executed = True
