import calendar
import psycopg2
import utils
import datetime
import warnings
import pandas as pd

warnings.simplefilter(action='ignore', category=FutureWarning)
import pandas

class DataAnalyzer:
    def __init__(self) -> None:
        config = utils.get_config()
        self.db_conn = psycopg2.connect(
            host=config.db_host,
            database=config.db_name,
            user=config.db_user,
            port=config.db_port
        )
        self.instruments_query = f"""
            SELECT symbol, lot_size from instruments
        """

    def get_pnl(self, instrument_list, day, month, year):
        month_name = calendar.month_abbr[month].upper()
        df_file_name = f'/Users/<USER>/Downloads/GDFLFNO_{year:04d}{month:02d}/GFDLNFO_BACKADJUSTED_{day:02d}{month:02d}{year:04d}.csv'
        df = pd.read_csv(df_file_name)
        df['time'] = pd.to_datetime(df['Date'] + ' ' + df['Time']+'+05:30', format='%d/%m/%Y %H:%M:%S%z')
        df['time'] = df['time'] + pd.Timedelta(seconds=1)
        df.rename(columns={'Ticker': 'symbol', 'Close': 'price', 'Volume': 'volume', 'Open Interest': 'oi'}, inplace=True)
        df['symbol'] = df['symbol'].str[:-4]
        df['symbol'] = df['symbol'].str.replace(r'(\d{2})(\w{3})(\d{2})', r'\3\2')

        df = df[df['symbol'].str.endswith(('CE', 'PE'))]
        df = df[df['symbol'].str.contains(month_name)] # Filter useless symbols
        df = df[['time', 'symbol', 'price', 'volume', 'oi']]
        df['spot_symbol'] = df['symbol'].str.extract(r'^([A-Za-z&-]+)')
        df['ce_pe'] = df['symbol'].str[-2:]
        df = df[~df['symbol'].str.contains('NIFTY')]
        df['prev_time'] = df.groupby('symbol')['time'].shift()
        df['prev_price'] = df.groupby('symbol')['price'].shift()

        df = df[df['prev_price'] != 0]
        df = df[df['oi'] != 0]

        df['max_price'] = df.groupby(['symbol', df['time'].dt.date])['price'].transform('max')
        df['last_price'] = df.groupby(['symbol', df['time'].dt.date])['price'].transform('last')

        df = df.dropna(subset=['prev_price'])
        df = df.dropna(subset=['oi'])
        df['traded_value'] = df['price'] * df['volume']
        df['is_widely_traded'] = df['traded_value'] > 1000000
        df['price_in_range'] = (df['price'] >= df['prev_price']) & (df['price'] <= df['prev_price'] * 1.1)
        df['can_trade'] = df['is_widely_traded'] & df['price_in_range']
        df = df[df['can_trade']==True]
        df = df.sort_values(by=['time', 'symbol'])
        df = df.drop_duplicates(subset=['spot_symbol', 'ce_pe', 'can_trade'], keep='first')

        df = df[['time', 'symbol', 'price', 'last_price', 'spot_symbol', 'ce_pe']]

        df['lot_size'] = df['spot_symbol'].apply(lambda x: instrument_list[x])
        df['buy_price'] = df['price'] * (30000 // df['price'] // df['lot_size']) * df['lot_size']
        df['sell_price'] = df['last_price'] * (30000 // df['price'] // df['lot_size']) * df['lot_size']
        df['pnl'] = df['sell_price'] - df['buy_price']
        return df['pnl'].sum()

    def get_trading_days(self, year, month, date_starting_from=None):
        # Get all the dates in a given month and year
        dates = []
        num_days = calendar.monthrange(year, month)[1]
        for day in range(1, num_days + 1):
            date = datetime.date(year, month, day)
            dates.append(date)
        holidays = ['2023-01-26', '2023-03-07', '2023-03-30', '2023-04-04', '2023-04-07', '2023-04-14', '2023-05-01', '2023-06-29', '2023-08-15', '2023-09-19', '2023-10-02', '2023-10-24', '2023-11-14', '2023-11-27', '2023-12-25']
        trading_days = [d for d in dates if d.weekday() < 5 and str(d) not in holidays]
        if date_starting_from:
            trading_days = [t for t in trading_days if t.day>=date_starting_from]
        return trading_days


# day = 7
# month = 11
# year = 2023

d = DataAnalyzer()
trading_days = d.get_trading_days(2023, 11)
instrument_list = {}
with d.db_conn.cursor() as cur:
    cur.execute(d.instruments_query)
    rows = cur.fetchall()
    instrument_list = {r[0]: r[1] for r in rows}

for t in trading_days:
    pnl = d.get_pnl(instrument_list=instrument_list, day=t.day, month=t.month, year=t.year)
    print("pnl: ", str(t.day)+"-"+str(t.month)+"-"+str(t.year)+": ", str(pnl))





# print(df.to_string())
